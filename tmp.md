🚨 Emergency Layout Fix - Applying immediately... index.html:331:17
🔧 تحميل إدارة الفئات مبكراً... index.html:451:17
✅ تم تحميل إدارة الفئات مبكراً index.html:666:17
🎯 الدالة الرئيسية متاحة: function index.html:667:17
🚨 Emergency Loading Fix activated emergency-loading-fix.js:9:13
✅ Emergency Loading Fix initialized emergency-loading-fix.js:116:13
🔧 Dashboard Coordination System loading... dashboard-coordination.js:9:13
✅ Dashboard Coordination System loaded successfully dashboard-coordination.js:165:13
✅ Main content layout fixed index.html:344:21
✅ Sidebar layout fixed index.html:354:21
🎉 Emergency layout fix applied successfully index.html:358:19
✅ Variable conflicts fix loaded successfully variable-conflicts-fix.js:49:9
🔧 API Fix Script loading... api-fix.js:6:9
✅ API Fix Script loaded successfully api-fix.js:318:9
⚡ Quick Fix Script loading... quick-fix.js:6:9
✅ Quick Fix Script loaded quick-fix.js:758:9
🔧 Navigation Fix Script loading... navigation-fix.js:6:9
✅ Navigation Fix Script loaded successfully navigation-fix.js:328:9
🚨 Emergency Navigation Fix loading... emergency-navigation-fix.js:6:9
✅ Emergency Navigation Fix loaded emergency-navigation-fix.js:358:9
🔧 Final Navigation Fix loading... final-navigation-fix.js:6:9
✅ Final Navigation Fix loaded successfully final-navigation-fix.js:461:9
🔍 Navigation Diagnostics loading... navigation-diagnostics.js:6:9
✅ Navigation Diagnostics loaded navigation-diagnostics.js:231:9
🔧 Comprehensive Fix Script loading... comprehensive-fix.js:6:9
✅ Comprehensive Fix Script loaded comprehensive-fix.js:445:9
🚀 Ultimate Fix Script loading... ultimate-fix.js:6:9
✅ Ultimate Fix Script loaded ultimate-fix.js:504:9
🎯 Specific Issues Fix Script loading... specific-issues-fix.js:6:9
✅ Specific Issues Fix Script loaded specific-issues-fix.js:333:9
🛡️ Ultimate Selection Error Fix loading... selection-error-fix.js:9:13
✅ Ultimate Selection Error Fix loaded successfully selection-error-fix.js:177:13
🔧 Loading Product Management Functions... product-management-functions.js:7:9
✅ Product Management Functions loaded successfully product-management-functions.js:256:9

- editProduct: Available product-management-functions.js:257:9
- viewProduct: Available product-management-functions.js:258:9
- deleteProduct: Available product-management-functions.js:259:9
- viewLandingPage: Available product-management-functions.js:260:9
  🔧 Functions are now globally accessible and ReferenceError issues should be resolved product-management-functions.js:261:9
  🛡️ Global error caught, ensuring content remains visible: expected expression, got '}' <anonymous code>:1:145535
  Uncaught SyntaxError: expected expression, got '}' admin.js:4170:65
  💳 Payment settings script loaded payment-settings.js:1950:9
  🔧 Loading critical fixes... critical-fixes.js:6:9
  ✅ Critical fixes loaded successfully critical-fixes.js:319:9
  🔧 Admin Navigation Fix loading... admin-sections-fix.js:9:13
  ✅ Admin Navigation Fix loaded successfully admin-sections-fix.js:350:13
  🏪 تحميل ملف store-settings-management.js... store-settings-management.js:6:9
  ✅ تم تحميل ملف store-settings-management.js بنجاح store-settings-management.js:456:9
  🎯 الدالة الرئيسية متاحة: function store-settings-management.js:457:9
  👥 تحميل ملف users-management.js... users-management.js:6:9
  ✅ تم تحميل ملف users-management.js بنجاح users-management.js:1660:9
  🎯 الدالة الرئيسية متاحة: function users-management.js:1661:9
  ✅ Security settings module loaded successfully security-settings.js:1849:9
  ✅ Subscription management module loaded successfully subscriptions-management.js:623:9
  📂 تحميل ملف categories-interactive.js... categories-interactive.js:6:9
  ✅ تم تحميل ملف categories-interactive.js بنجاح categories-interactive.js:614:9
  🔧 الدوال المتاحة:
  Object { loadInteractiveCategoriesContent: "function", showAddCategoryModal: "function", editCategory: "function", saveCategory: "function", deleteCategory: "function" }
  categories-interactive.js:615:9
  🧹 Dashboard Cleanup Script loading... dashboard-cleanup.js:9:13
  ✅ Dashboard Cleanup Script loaded successfully dashboard-cleanup.js:321:13
  🔧 Products Loader Fix loading... products-loader-fix.js:9:13
  ✅ Products Loader Fix loaded successfully products-loader-fix.js:289:13
  🔧 Products Pagination System loading... products-pagination.js:17:13
  ✅ Product management functions ensured to be available products-pagination.js:456:13
- editProduct: function products-pagination.js:457:13
- viewProduct: function products-pagination.js:458:13
- deleteProduct: function products-pagination.js:459:13
- viewLandingPage: function products-pagination.js:460:13
  ✅ Products Pagination System loaded successfully products-pagination.js:469:13
  🔧 Multi-User Admin Interface loading... multi-user-admin-interface.js:9:13
  ✅ Multi-User Admin Interface loaded successfully multi-user-admin-interface.js:506:13
  📊 Reports and Statistics module loading... reports-statistics.js:9:13
  ✅ Reports and Statistics module loaded successfully reports-statistics.js:436:13
  🔧 تحميل ملف categories-working.js... categories-working.js:6:9
  ✅ تم تحميل ملف categories-working.js بنجاح categories-working.js:325:9
  🎯 الدالة الرئيسية متاحة: function categories-working.js:326:9
  🔧 تحميل إدارة الفئات مباشرة... index.html:4801:17
  ✅ تم تحميل إدارة الفئات مباشرة index.html:5015:17
  🎯 الدالة الرئيسية متاحة: function index.html:5016:17
  🚨 Emergency submenu visibility fix activated index.html:5188:21
  ✅ Item 1 forced visible: الإعدادات العامة index.html:5224:29
  ✅ Item 2 forced visible: إعدادات الدفع index.html:5224:29
  ✅ Item 3 forced visible: إدارة الفئات index.html:5224:29
  ✅ Item 4 forced visible: إدارة المستخدمين index.html:5224:29
  ✅ Item 5 forced visible: إدارة الأدوار index.html:5224:29
  ✅ Item 6 forced visible: عرض النظام المحسن index.html:5224:29
  ✅ Item 7 forced visible: إعدادات المتجر index.html:5224:29
  ✅ Item 8 forced visible: إدارة المتاجر index.html:5224:29
  ✅ Item 9 forced visible: إعدادات الأمان index.html:5224:29
  ✅ Item 10 forced visible: إدارة الاشتراكات index.html:5224:29
  ✅ Submenu forced visible with 10 items index.html:5227:25
  ✅ Menu forced to expanded state index.html:5236:25
  🚨 Emergency submenu visibility fix scripts loaded index.html:5251:17
  🏪 Stores management script loaded stores-management.js:1055:9
  ✅ Main content layout fixed index.html:344:21
  ✅ Sidebar layout fixed index.html:354:21
  🎉 Emergency layout fix applied successfully index.html:358:19
  🔧 Forcing admin content to show... emergency-loading-fix.js:16:17
  ✅ Loading indicator hidden emergency-loading-fix.js:26:25
  ✅ Main content forced visible emergency-loading-fix.js:39:25
  ✅ Sidebar forced visible emergency-loading-fix.js:48:25
  ✅ 20 content sections forced visible emergency-loading-fix.js:60:25
  🎉 Emergency loading fix completed successfully emergency-loading-fix.js:76:21
  🚀 Initializing dashboard coordination system... dashboard-coordination.js:129:17
  ✅ Enhanced dashboard detected by coordination system dashboard-coordination.js:40:25
  🛡️ Dashboard cleanup disabled dashboard-coordination.js:53:21
  ✅ Dashboard coordination system initialized dashboard-coordination.js:150:17
  ✅ API Fix Script initialized api-fix.js:271:13
  ⚡ Quick Fix - DOM Ready quick-fix.js:335:13
  🔧 Navigation Fix - DOM Content Loaded navigation-fix.js:303:13
  🚨 Emergency Navigation Fix - DOM Ready emergency-navigation-fix.js:337:13
  🔧 Final Navigation Fix - DOM Ready final-navigation-fix.js:420:13
  🔧 Comprehensive Fix - DOM Ready comprehensive-fix.js:434:13
  🚀 Ultimate Fix - DOM Ready ultimate-fix.js:492:13
  🎯 Specific Issues Fix - DOM Ready specific-issues-fix.js:322:13
  ✅ Shared utilities initialized utils.js:509:13
  🌐 API Call: ../api/get-ai-settings.php utils.js:14:17
  🌐 API Call: ../api/get-ai-settings.php api-fix.js:14:17
  💳 Payment settings DOM loaded payment-settings.js:183:13
  🚀 Initializing critical fixes... critical-fixes.js:18:17
  🔧 Fixing product management functions... critical-fixes.js:36:17
  ✅ Product management functions fixed critical-fixes.js:180:17
  🔧 Fixing pagination functions... critical-fixes.js:184:17
  ✅ Pagination functions fixed critical-fixes.js:227:17
  🔧 Fixing settings sections... critical-fixes.js:231:17
  ✅ Settings sections fixed critical-fixes.js:288:17
  🔧 Fixing reports section... critical-fixes.js:292:17
  ✅ Reports section fixed critical-fixes.js:315:17
  ✅ Critical fixes initialized critical-fixes.js:32:17
  🔒 تهيئة إعدادات الأمان... security-settings.js:303:13
  GET
  http://localhost:8000/favicon.ico
  [HTTP/1 404 File not found 0ms]

🌐 API Call: ../api/security-settings.php api-fix.js:14:17
✅ Security settings initialized security-settings.js:534:13
🌐 API Call: php/api/security-settings.php?action=dashboard api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=dashboard → php/api/security-settings.php?action=dashboard api-fix.js:31:29
🌐 API Call: php/api/security-settings.php?action=password_policy api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=password_policy → php/api/security-settings.php?action=password_policy api-fix.js:31:29
🌐 API Call: php/api/security-settings.php?action=threat_detection api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=threat_detection → php/api/security-settings.php?action=threat_detection api-fix.js:31:29
📦 Products Loader Fix ready products-loader-fix.js:280:17
🚀 Initializing products pagination... products-pagination.js:21:17
📦 Loading products with pagination... products-pagination.js:36:21
🌐 API Call: ../php/api/products.php api-fix.js:14:17
✅ Multi-User Admin Interface ready multi-user-admin-interface.js:486:17
✅ Enhanced dashboard already present - skipping multi-user dashboard multi-user-admin-interface.js:495:25
✅ Reports and Statistics module ready reports-statistics.js:427:17
🚀 Initializing Enhanced Admin Settings Menu from external script... admin-settings-menu-enhanced.js:397:13
🚀 Initializing Enhanced Admin Settings Menu... admin-settings-menu-enhanced.js:21:17
✅ Enhanced Admin Settings Menu initialized successfully admin-settings-menu-enhanced.js:60:17
✅ Enhanced Admin Settings Menu available globally admin-settings-menu-enhanced.js:403:13
🌐 API Call: /api/get-ai-settings.php api-fix.js:14:17
🚨 Emergency submenu visibility fix activated index.html:5188:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5224:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5224:29
✅ Item 3 forced visible: إدارة الفئات index.html:5224:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5224:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5224:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5224:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5224:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5224:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5224:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5224:29
✅ Submenu forced visible with 10 items index.html:5227:25
✅ Menu forced to expanded state index.html:5236:25
🛡️ Activating enhanced dashboard protection system index.html:5777:19
✅ Dashboard protection system activated index.html:5793:19
🔄 Window load event fired, ensuring content is visible emergency-loading-fix.js:106:17
🔧 Navigation Fix - Window Loaded navigation-fix.js:318:13
🚨 Emergency Navigation Fix - Window Loaded emergency-navigation-fix.js:349:13
🔧 Final Navigation Fix - Window Loaded final-navigation-fix.js:444:13
TinyMCE config initialization started tinymce-config.js:27:13
Initializing TinyMCE with configuration... tinymce-config.js:73:13
TinyMCE initialization successful tinymce-config.js:201:17
No TinyMCE textareas found on page <anonymous code>:1:145535
💳 Initializing Payment Settings... payment-settings.js:76:13
Loading: جاري تحميل إعدادات الدفع... 2 stores-management.js:487:13
🌐 API Call: ../php/api/payment-settings.php api-fix.js:14:17
✅ Main content layout fixed index.html:344:21
✅ Sidebar layout fixed index.html:354:21
🎉 Emergency layout fix applied successfully index.html:358:19
🧹 Forcing clean navigation state... admin-sections-fix.js:273:17
✅ Clean state enforced with !important styles admin-sections-fix.js:302:17
🚀 Initializing navigation system... admin-sections-fix.js:22:17
📄 Showing section: dashboard admin-sections-fix.js:51:17
📦 Loading content for section: dashboard admin-sections-fix.js:153:17
✅ Enhanced dashboard detected - preserving existing content admin-sections-fix.js:164:29
✅ Section dashboard is now active admin-sections-fix.js:66:21
🔗 Setting up navigation listeners... admin-sections-fix.js:89:17
Found 17 navigation items admin-sections-fix.js:93:17
✅ Listener added to: reportsContent admin-sections-fix.js:112:21
✅ Listener added to: storesManagementContent admin-sections-fix.js:112:21
✅ Listener added to: dashboard admin-sections-fix.js:112:21
✅ Listener added to: books admin-sections-fix.js:112:21
✅ Listener added to: orders admin-sections-fix.js:112:21
✅ Listener added to: landingPages admin-sections-fix.js:112:21
✅ Listener added to: reportsContent admin-sections-fix.js:112:21
✅ Listener added to: generalSettings admin-sections-fix.js:112:21
✅ Listener added to: paymentSettings admin-sections-fix.js:112:21
✅ Listener added to: categories admin-sections-fix.js:112:21
✅ Listener added to: usersManagement admin-sections-fix.js:112:21
✅ Listener added to: rolesManagement admin-sections-fix.js:112:21
✅ Listener added to: showcaseDemo admin-sections-fix.js:112:21
✅ Listener added to: storeSettings admin-sections-fix.js:112:21
✅ Listener added to: storesManagementContent admin-sections-fix.js:112:21
✅ Listener added to: securitySettings admin-sections-fix.js:112:21
✅ Listener added to: subscriptionsManagement admin-sections-fix.js:112:21
✅ Navigation listeners setup complete admin-sections-fix.js:132:17
✅ Navigation system initialized admin-sections-fix.js:33:17
🔄 Auto-expanding admin settings menu... admin-settings-menu-enhanced.js:48:21
📈 Expanding admin settings menu... admin-settings-menu-enhanced.js:179:17
✅ Admin settings menu expanded admin-settings-menu-enhanced.js:198:17
JSON parse error for response: <?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: \*');
header('Access-Control-Allow-Methods: GET');

// Prevent any output <anonymous code>:1:145535
❌ API call failed: ../api/get-ai-settings.php Error: Invalid JSON response from server
safeApiCall http://localhost:8000/js/utils.js:50
checkAvailability http://localhost:8000/admin/js/ai-magic-wand.js:45
init http://localhost:8000/admin/js/ai-magic-wand.js:15
AIMagicWand http://localhost:8000/admin/js/ai-magic-wand.js:11
<anonymous> http://localhost:8000/admin/js/ai-magic-wand.js:735
EventListener.handleEvent* http://localhost:8000/admin/js/ai-magic-wand.js:734
<anonymous code>:1:145535
AI availability check failed: Error: Invalid JSON response from server
safeApiCall http://localhost:8000/js/utils.js:50
checkAvailability http://localhost:8000/admin/js/ai-magic-wand.js:45
init http://localhost:8000/admin/js/ai-magic-wand.js:15
AIMagicWand http://localhost:8000/admin/js/ai-magic-wand.js:11
<anonymous> http://localhost:8000/admin/js/ai-magic-wand.js:735
EventListener.handleEvent* http://localhost:8000/admin/js/ai-magic-wand.js:734
<anonymous code>:1:145535
🪄 AI Magic Wand initialized ai-magic-wand.js:22:17
❌ Error loading security settings: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error loading security dashboard: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error loading password policy: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error loading threat detection settings: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
محاولة 1/3 فشلت: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error loading products: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error loading payment settings: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error: خطأ في تحميل إعدادات الدفع stores-management.js:537:13
Loading finished stores-management.js:508:13
✅ Payment Settings initialized successfully payment-settings.js:92:17
Success: تم تحميل إعدادات الدفع بنجاح stores-management.js:530:13
Loading finished stores-management.js:508:13
🚨 Emergency submenu visibility fix activated index.html:5188:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5224:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5224:29
✅ Item 3 forced visible: إدارة الفئات index.html:5224:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5224:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5224:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5224:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5224:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5224:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5224:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5224:29
✅ Submenu forced visible with 10 items index.html:5227:25
✅ Menu forced to expanded state index.html:5236:25
🚨 Applying emergency navigation fix... emergency-navigation-fix.js:10:13
Found 18 items for selector: .admin-nav ul li emergency-navigation-fix.js:22:17
✅ Emergency fix applied to item 0: التقارير والإحصائيات emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 1: إدارة المتاجر emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 2: الرئيسية emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 3: إدارة المنتجات emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 4: الطلبات emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 5: صفحات هبوط emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 6: التقارير والإحصائيات emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 7: إعدادات الإدارة

                  الإعدادات العامة



                  إعدادات الدفع



                  إدارة الفئات



                  إدارة المستخدمين



                  إدارة الأدوار



                  عرض النظام المحسن



                  إعدادات المتجر



                  إدارة المتاجر



                  إعدادات الأمان



                  إدارة الاشتراكات emergency-navigation-fix.js:72:21

✅ Emergency fix applied to item 8: الإعدادات العامة emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 9: إعدادات الدفع emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 10: إدارة الفئات emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 11: إدارة المستخدمين emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 12: إدارة الأدوار emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 13: عرض النظام المحسن emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 14: إعدادات المتجر emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 15: إدارة المتاجر emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 16: إعدادات الأمان emergency-navigation-fix.js:72:21
✅ Emergency fix applied to item 17: إدارة الاشتراكات emergency-navigation-fix.js:72:21
Found 16 items for selector: .sidebar ul li emergency-navigation-fix.js:22:17
Found 18 items for selector: nav ul li emergency-navigation-fix.js:22:17
Found 17 items for selector: [data-section] emergency-navigation-fix.js:22:17
finalNavigationFix function not found <anonymous code>:1:145535
Erreur dans les liens source : Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
✅ Main content layout fixed index.html:344:21
✅ Sidebar layout fixed index.html:354:21
🎉 Emergency layout fix applied successfully index.html:358:19
🔄 Force initializing navigation... navigation-fix.js:10:13
Found 18 navigation items navigation-fix.js:14:13
Processing navigation item 0:

<li data-section="reportsContent" onclick="showAdminSection('reportsContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 1:
<li data-section="storesManagementContent" onclick="showAdminSection('storesManagementContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 2:
<li class="active" data-section="dashboard" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 3:
<li data-section="books" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 4:
<li data-section="orders" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 5:
<li data-section="landingPages" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 6:
<li data-section="reportsContent" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 7:
<li class="admin-settings-menu expanded" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 8:
<li data-section="generalSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 9:
<li data-section="paymentSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 10:
<li data-section="categories" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 11:
<li data-section="usersManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 12:
<li data-section="rolesManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 13:
<li data-section="showcaseDemo" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 14:
<li data-section="storeSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 15:
<li data-section="storesManagementContent" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 16:
<li data-section="securitySettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
Processing navigation item 17:
<li data-section="subscriptionsManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true">
navigation-fix.js:23:17
✅ Navigation initialization complete navigation-fix.js:71:13
🚨 Emergency submenu visibility fix activated index.html:5188:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5224:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5224:29
✅ Item 3 forced visible: إدارة الفئات index.html:5224:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5224:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5224:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5224:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5224:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5224:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5224:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5224:29
✅ Submenu forced visible with 10 items index.html:5227:25
✅ Menu forced to expanded state index.html:5236:25
🔧 Final admin settings menu fix... index.html:5351:21
✅ Admin settings menu fully fixed and functional index.html:5397:25
⚡ Applying quick fixes... quick-fix.js:10:13
⚡ Quick fixes applied:
Array [ "Fixed 17 navigation items", "Created 3 missing functions: loadOrders, loadLandingPages, loadReportsAndStatistics" ]
quick-fix.js:29:17
Unhandled promise rejection: TypeError: can't access property "push", results.errors is undefined
    fixJavaScriptErrors http://localhost:8000/admin/js/comprehensive-fix.js:247
    applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:20
    setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:338
    EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
<anonymous code>:1:145535
Unhandled promise rejection: TypeError: can't access property "push", results.errors is undefined
    fixUIIssues http://localhost:8000/admin/js/comprehensive-fix.js:333
    applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:26
    setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:338
    EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
<anonymous code>:1:145535
🔄 Force initializing navigation... navigation-fix.js:10:13
Found 18 navigation items navigation-fix.js:14:13
Processing navigation item 0:
<li data-section="reportsContent" onclick="showAdminSection('reportsContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 1:
<li data-section="storesManagementContent" onclick="showAdminSection('storesManagementContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 2:
<li class="active" data-section="dashboard" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 3:
<li data-section="books" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 4:
<li data-section="orders" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 5:
<li data-section="landingPages" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 6:
<li data-section="reportsContent" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 7:
<li class="admin-settings-menu expanded" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 8:
<li data-section="generalSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 9:
<li data-section="paymentSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 10:
<li data-section="categories" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 11:
<li data-section="usersManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 12:
<li data-section="rolesManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 13:
<li data-section="showcaseDemo" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 14:
<li data-section="storeSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 15:
<li data-section="storesManagementContent" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 16:
<li data-section="securitySettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 17:
<li data-section="subscriptionsManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
✅ Navigation initialization complete navigation-fix.js:71:13
Uncaught (in promise) TypeError: can't access property "push", results.errors is undefined
    fixJavaScriptErrors http://localhost:8000/admin/js/comprehensive-fix.js:247
    applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:20
    setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:338
    EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
comprehensive-fix.js:247:9
Uncaught (in promise) TypeError: can't access property "push", results.errors is undefined
    fixUIIssues http://localhost:8000/admin/js/comprehensive-fix.js:333
    applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:26
    setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:338
    EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
comprehensive-fix.js:333:9
📊 Loading reports and statistics... reports-statistics.js:21:17
🌐 API Call: ../php/api/admin-dashboard.php?action=overview&user_id=1&user_role=admin api-fix.js:14:17
🌐 API Call: ../php/api/dashboard-stats.php api-fix.js:14:17
🌐 API Call: ../php/api/products-multi-user.php?user_id=1&user_role=admin api-fix.js:14:17
🌐 API Call: ../php/api/admin-dashboard.php?action=users&user_id=1&user_role=admin api-fix.js:14:17
❌ Error loading reports: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
🚨 Applying emergency navigation fix... emergency-navigation-fix.js:10:13
Found 18 items for selector: .admin-nav ul li emergency-navigation-fix.js:22:17
Found 16 items for selector: .sidebar ul li emergency-navigation-fix.js:22:17
Found 18 items for selector: nav ul li emergency-navigation-fix.js:22:17
Found 17 items for selector: [data-section] emergency-navigation-fix.js:22:17
🌐 API Call: /api/get-ai-settings.php api-fix.js:14:17
محاولة 2/3 فشلت: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
🚨 Emergency submenu visibility fix activated index.html:5188:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5224:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5224:29
✅ Item 3 forced visible: إدارة الفئات index.html:5224:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5224:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5224:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5224:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5224:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5224:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5224:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5224:29
✅ Submenu forced visible with 10 items index.html:5227:25
✅ Menu forced to expanded state index.html:5236:25
🌐 API Call: ../php/api/users.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/roles-fixed.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/categories-fixed.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/products.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/stores.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/subscriptions-fixed.php?action=plans api-fix.js:14:17
🌐 API Call: ../php/api/users.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/roles-fixed.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/categories-fixed.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/products.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/stores.php?action=list api-fix.js:14:17
🌐 API Call: ../php/api/subscriptions-fixed.php?action=plans api-fix.js:14:17
🚨 Applying emergency navigation fix... emergency-navigation-fix.js:10:13
Found 18 items for selector: .admin-nav ul li emergency-navigation-fix.js:22:17
Found 16 items for selector: .sidebar ul li emergency-navigation-fix.js:22:17
Found 18 items for selector: nav ul li emergency-navigation-fix.js:22:17
Found 17 items for selector: [data-section] emergency-navigation-fix.js:22:17
API test failed for المستخدمين: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الأدوار: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الفئات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for المنتجات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for المتاجر: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الاشتراكات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
🔍 Database connectivity test completed: 0/6 APIs working (0.0%) index.html:5343:21
API test failed for المستخدمين: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الأدوار: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الفئات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for المنتجات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for المتاجر: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API test failed for الاشتراكات: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
🔍 Database connectivity test completed: 0/6 APIs working (0.0%) index.html:5343:21
⚡ Applying quick fixes... quick-fix.js:10:13
✅ JavaScript errors fixed comprehensive-fix.js:244:17
⚡ Quick fixes applied:
Array []
quick-fix.js:29:17
🔄 Force initializing navigation... navigation-fix.js:10:13
Found 18 navigation items navigation-fix.js:14:13
Processing navigation item 0:
<li data-section="reportsContent" onclick="showAdminSection('reportsContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 1:
<li data-section="storesManagementContent" onclick="showAdminSection('storesManagementContent')" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 2:
<li class="active" data-section="dashboard" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 3:
<li data-section="books" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 4:
<li data-section="orders" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 5:
<li data-section="landingPages" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 6:
<li data-section="reportsContent" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 7:
<li class="admin-settings-menu expanded" data-emergency-fixed="true" style="cursor: pointer; pointer…uto; user-select: none;">
navigation-fix.js:23:17
Processing navigation item 8:
<li data-section="generalSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 9:
<li data-section="paymentSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 10:
<li data-section="categories" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 11:
<li data-section="usersManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 12:
<li data-section="rolesManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 13:
<li data-section="showcaseDemo" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 14:
<li data-section="storeSettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 15:
<li data-section="storesManagementContent" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 16:
<li data-section="securitySettings" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
Processing navigation item 17:
<li data-section="subscriptionsManagement" style="display: flex; visibilit…uto; user-select: none;" data-emergency-fixed="true" data-click-handler="true" data-quick-fixed="true">
navigation-fix.js:23:17
✅ Navigation initialization complete navigation-fix.js:71:13
Unhandled promise rejection: TypeError: can't access property "push", results.errors is undefined
    fixUIIssues http://localhost:8000/admin/js/comprehensive-fix.js:333
    applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:26
    setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:339
    EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
<anonymous code>:1:145535
🔍 Running navigation diagnostics... navigation-diagnostics.js:9:13
Found 18 items for selector: .admin-nav ul li navigation-diagnostics.js:29:17
Skipping container item: إعدادات الإدارة

... navigation-diagnostics.js:36:25
Found 16 items for selector: .sidebar ul li navigation-diagnostics.js:29:17
Skipping container item: إعدادات الإدارة

... navigation-diagnostics.js:36:25
Found 18 items for selector: nav ul li navigation-diagnostics.js:29:17
Skipping container item: إعدادات الإدارة

... navigation-diagnostics.js:36:25
Found 17 items for selector: [data-section] navigation-diagnostics.js:29:17
📊 Navigation Diagnostics Results:
Object { timestamp: "2025-07-24T17:27:27.770Z", navItems: (66) […], sections: (37) […], errors: [], warnings: [] }
navigation-diagnostics.js:110:13
Uncaught (in promise) TypeError: can't access property "push", results.errors is undefined
fixUIIssues http://localhost:8000/admin/js/comprehensive-fix.js:333
applyQuickFixes http://localhost:8000/admin/js/quick-fix.js:26
setTimeout handler* http://localhost:8000/admin/js/quick-fix.js:339
EventListener.handleEvent* http://localhost:8000/admin/js/quick-fix.js:334
comprehensive-fix.js:333:9
🔧 Running comprehensive fix... comprehensive-fix.js:17:13
🔧 Fixing database schema... comprehensive-fix.js:28:17
🔍 Testing database connection... comprehensive-fix.js:65:17
🌐 API Call: ../php/api/test-database-connection.php api-fix.js:14:17
🎯 Running specific issues fix... specific-issues-fix.js:10:13
1️⃣ Fixing navigation menu text concatenation... specific-issues-fix.js:21:17
2️⃣ Testing and fixing roles API... specific-issues-fix.js:25:17
🌐 API Call: ../php/api/roles.php api-fix.js:14:17
❌ Database fix error: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
🔧 Fixing navigation... comprehensive-fix.js:32:17
🔧 Applying final navigation fix... final-navigation-fix.js:41:13
Processing 18 items for selector: .admin-nav ul li final-navigation-fix.js:48:17
✅ Fixed navigation item: التقارير والإحصائيات final-navigation-fix.js:97:17
✅ Fixed navigation item: إدارة المتاجر final-navigation-fix.js:97:17
✅ Fixed navigation item: الرئيسية final-navigation-fix.js:97:17
✅ Fixed navigation item: إدارة المنتجات final-navigation-fix.js:97:17
✅ Fixed navigation item: الطلبات final-navigation-fix.js:97:17
✅ Fixed navigation item: صفحات هبوط final-navigation-fix.js:97:17
✅ Fixed navigation item: التقارير والإحصائيات final-navigation-fix.js:97:17
✅ Fixed navigation item: إعدادات الإدارة

                  الإعدادات العامة



                  إعدادات الدفع



                  إدارة الفئات



                  إدارة المستخدمين



                  إدارة الأدوار



                  عرض النظام المحسن



                  إعدادات المتجر



                  إدارة المتاجر



                  إعدادات الأمان



                  إدارة الاشتراكات final-navigation-fix.js:97:17

Processing 16 items for selector: .sidebar ul li final-navigation-fix.js:48:17
Processing 18 items for selector: nav ul li final-navigation-fix.js:48:17
Processing 17 items for selector: [data-section] final-navigation-fix.js:48:17
✅ Fixed 8 navigation items final-navigation-fix.js:57:13
✅ Navigation fixed comprehensive-fix.js:172:17
🔧 Fixing JavaScript errors... comprehensive-fix.js:36:17
✅ JavaScript errors fixed comprehensive-fix.js:244:17
🔧 Fixing API calls... comprehensive-fix.js:40:17
🌐 API Call: ../php/api/admin-dashboard.php?action=overview api-fix.js:14:17
3️⃣ Fixing products API data structure... specific-issues-fix.js:29:17
🌐 API Call: ../php/api/products-multi-user.php?user_id=1&user_role=admin api-fix.js:14:17
🌐 API Call: /api/get-ai-settings.php api-fix.js:14:17
🌐 API Call: ../php/api/products-multi-user.php api-fix.js:14:17
4️⃣ Ensuring roles table is populated... specific-issues-fix.js:33:17
🌐 API Call: ../php/api/populate-roles.php api-fix.js:14:17
محاولة 3/3 فشلت: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data stores-management.js:537:13
🌐 API Call: ../php/api/dashboard-stats.php api-fix.js:14:17
🎯 Specific issues fix completed:
Object { timestamp: "2025-07-24T17:27:27.782Z", fixes: (2) […], errors: (2) […], warnings: (1) […] }
specific-issues-fix.js:39:17
🌐 API Call: ../php/api/roles.php api-fix.js:14:17
✅ API calls tested comprehensive-fix.js:282:17
🔧 Fixing UI issues... comprehensive-fix.js:44:17
✅ UI issues fixed comprehensive-fix.js:330:17
✅ Comprehensive fix completed:
Object { timestamp: "2025-07-24T17:27:27.781Z", fixes: (3) […], errors: (1) […], warnings: [] }
comprehensive-fix.js:50:17
⏰ Final fallback: Ensuring content is visible after 5 seconds emergency-loading-fix.js:100:17
🚨 Applying emergency navigation fix... emergency-navigation-fix.js:10:13
Found 18 items for selector: .admin-nav ul li emergency-navigation-fix.js:22:17
Found 16 items for selector: .sidebar ul li emergency-navigation-fix.js:22:17
Found 18 items for selector: nav ul li emergency-navigation-fix.js:22:17
Found 17 items for selector: [data-section] emergency-navigation-fix.js:22:17
🚀 Running ultimate fix... ultimate-fix.js:10:13
1️⃣ Populating roles table... ultimate-fix.js:21:17
🌐 API Call: ../php/api/fix-roles-table.php api-fix.js:14:17
2️⃣ Fixing navigation structure... ultimate-fix.js:25:17
3️⃣ Fixing JavaScript errors... ultimate-fix.js:29:17
4️⃣ Testing APIs... ultimate-fix.js:33:17
🌐 API Call: ../php/api/test-database-connection.php api-fix.js:14:17
🌐 API Call: ../php/api/test-roles-api.php api-fix.js:14:17
🌐 API Call: ../php/api/dashboard-stats.php api-fix.js:14:17
🌐 API Call: ../php/api/products.php api-fix.js:14:17
🚀 Ultimate fix completed:
Object { timestamp: "2025-07-24T17:27:29.756Z", fixes: (3) […], errors: (1) […], warnings: [] }
ultimate-fix.js:39:17
