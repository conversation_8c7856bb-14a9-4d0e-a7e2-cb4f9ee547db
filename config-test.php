<?php
header('Content-Type: application/json; charset=utf-8');

try {
    // Test config inclusion
    require_once __DIR__ . '/php/config.php';
    
    $response = [
        'success' => true,
        'message' => 'Config loaded successfully',
        'has_conn' => isset($conn),
        'conn_type' => isset($conn) ? get_class($conn) : 'null'
    ];
    
    if (isset($conn)) {
        // Test database connection
        $stmt = $conn->query('SELECT 1 as test');
        $result = $stmt->fetch();
        $response['db_test'] = $result ? 'success' : 'failed';
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>