<?php
/**
 * Categories API - Fixed Version
 * Handles all category-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$dbname = 'poultraydz';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';

try {
    switch ($action) {
        case 'list':
            handleListCategories($pdo);
            break;
        case 'create':
            handleCreateCategory($pdo);
            break;
        case 'update':
            handleUpdateCategory($pdo);
            break;
        case 'delete':
            handleDeleteCategory($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListCategories($pdo) {
    try {
        // Create categories table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                parent_id INT DEFAULT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
        $count = $countStmt->fetch()['count'];
        
        if ($count == 0) {
            $sampleCategories = [
                ['name' => 'الإلكترونيات', 'description' => 'أجهزة إلكترونية متنوعة'],
                ['name' => 'الملابس', 'description' => 'ملابس رجالية ونسائية'],
                ['name' => 'الكتب', 'description' => 'كتب ومراجع متنوعة'],
                ['name' => 'المنزل والحديقة', 'description' => 'أدوات منزلية ومستلزمات الحديقة'],
                ['name' => 'الرياضة', 'description' => 'معدات ومستلزمات رياضية']
            ];
            
            $insertStmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
            foreach ($sampleCategories as $category) {
                $insertStmt->execute([$category['name'], $category['description']]);
            }
        }

        // Fetch all categories
        $stmt = $pdo->query("SELECT * FROM categories ORDER BY created_at DESC");
        $categories = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => [
                'categories' => $categories,
                'total' => count($categories)
            ],
            'message' => 'Categories retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve categories: ' . $e->getMessage());
    }
}

function handleCreateCategory($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Category name is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO categories (name, description, parent_id, status) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['parent_id'] ?? null,
            $input['status'] ?? 'active'
        ]);

        $categoryId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $categoryId,
                'name' => $input['name']
            ],
            'message' => 'Category created successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create category: ' . $e->getMessage());
    }
}

function handleUpdateCategory($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id']) || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Category ID and name are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("UPDATE categories SET name = ?, description = ?, parent_id = ?, status = ? WHERE id = ?");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['parent_id'] ?? null,
            $input['status'] ?? 'active',
            $input['id']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $input['id'],
                'name' => $input['name']
            ],
            'message' => 'Category updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update category: ' . $e->getMessage());
    }
}

function handleDeleteCategory($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Category ID is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete category: ' . $e->getMessage());
    }
}
?>
