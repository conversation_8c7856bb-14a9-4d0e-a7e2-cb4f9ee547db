<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Stability Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .navigation-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .nav-button {
            background: #48bb78;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        .nav-button:hover {
            background: #38a169;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> Dashboard Stability Test</h1>
            <p>اختبار شامل لاستقرار لوحة التحكم المحسنة</p>
        </div>

        <!-- Dashboard Protection Test -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> اختبار حماية لوحة التحكم</h3>
            <button class="test-button" onclick="testDashboardProtection()">
                <i class="fas fa-play"></i> تشغيل اختبار الحماية
            </button>
            <div id="protectionTestResult"></div>
        </div>

        <!-- Navigation Test -->
        <div class="test-section">
            <h3><i class="fas fa-compass"></i> اختبار التنقل</h3>
            <p>اختبار جميع روابط التنقل في الشريط الجانبي:</p>
            <div class="navigation-test">
                <button class="nav-button" onclick="testNavigation('dashboard')">الرئيسية</button>
                <button class="nav-button" onclick="testNavigation('books')">إدارة المنتجات</button>
                <button class="nav-button" onclick="testNavigation('orders')">الطلبات</button>
                <button class="nav-button" onclick="testNavigation('landingPages')">صفحات هبوط</button>
                <button class="nav-button" onclick="testNavigation('reportsContent')">التقارير</button>
                <button class="nav-button" onclick="testNavigation('generalSettings')">الإعدادات العامة</button>
                <button class="nav-button" onclick="testNavigation('paymentSettings')">إعدادات الدفع</button>
                <button class="nav-button" onclick="testNavigation('categories')">إدارة الفئات</button>
                <button class="nav-button" onclick="testNavigation('usersManagement')">إدارة المستخدمين</button>
                <button class="nav-button" onclick="testNavigation('rolesManagement')">إدارة الأدوار</button>
                <button class="nav-button" onclick="testNavigation('showcaseDemo')">عرض النظام</button>
                <button class="nav-button" onclick="testNavigation('storeSettings')">إعدادات المتجر</button>
                <button class="nav-button" onclick="testNavigation('storesManagementContent')">إدارة المتاجر</button>
                <button class="nav-button" onclick="testNavigation('securitySettings')">إعدادات الأمان</button>
                <button class="nav-button" onclick="testNavigation('subscriptionsManagement')">إدارة الاشتراكات</button>
            </div>
            <div id="navigationTestResult"></div>
        </div>

        <!-- Stability Test -->
        <div class="test-section">
            <h3><i class="fas fa-heartbeat"></i> اختبار الاستقرار</h3>
            <button class="test-button" onclick="testStability()">
                <i class="fas fa-play"></i> تشغيل اختبار الاستقرار
            </button>
            <div id="stabilityTestResult"></div>
        </div>

        <!-- Theme Test -->
        <div class="test-section">
            <h3><i class="fas fa-palette"></i> اختبار تغيير المظهر</h3>
            <button class="test-button" onclick="testThemeToggle()">
                <i class="fas fa-play"></i> اختبار تغيير المظهر
            </button>
            <div id="themeTestResult"></div>
        </div>

        <!-- Results Summary -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check"></i> ملخص النتائج</h3>
            <button class="test-button" onclick="runAllTests()">
                <i class="fas fa-play-circle"></i> تشغيل جميع الاختبارات
            </button>
            <div id="summaryResult"></div>
        </div>
    </div>

    <script>
        let testResults = {
            protection: false,
            navigation: 0,
            stability: false,
            theme: false
        };

        function testDashboardProtection() {
            const resultDiv = document.getElementById('protectionTestResult');
            resultDiv.innerHTML = '<div class="warning">جاري اختبار الحماية...</div>';

            // Open admin page in new window
            const adminWindow = window.open('index.html', '_blank');
            
            setTimeout(() => {
                try {
                    const dashboard = adminWindow.document.getElementById('dashboard');
                    const enhancedDashboard = dashboard ? dashboard.querySelector('.dashboard-header') : null;
                    const protectionActive = adminWindow.dashboardProtection ? adminWindow.dashboardProtection.protectionActive : false;

                    if (enhancedDashboard && protectionActive) {
                        resultDiv.innerHTML = '<div class="success">✅ نظام الحماية يعمل بشكل صحيح</div>';
                        testResults.protection = true;
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ نظام الحماية لا يعمل بشكل صحيح</div>';
                    }
                } catch (error) {
                    resultDiv.innerHTML = '<div class="error">❌ خطأ في اختبار الحماية: ' + error.message + '</div>';
                }
            }, 3000);
        }

        function testNavigation(sectionId) {
            const resultDiv = document.getElementById('navigationTestResult');
            
            // Open admin page and test navigation
            const adminWindow = window.open('index.html', '_blank');
            
            setTimeout(() => {
                try {
                    const section = adminWindow.document.getElementById(sectionId);
                    if (section) {
                        resultDiv.innerHTML += `<div class="success">✅ ${sectionId}: موجود</div>`;
                        testResults.navigation++;
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ ${sectionId}: غير موجود</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">❌ ${sectionId}: خطأ - ${error.message}</div>`;
                }
            }, 2000);
        }

        function testStability() {
            const resultDiv = document.getElementById('stabilityTestResult');
            resultDiv.innerHTML = '<div class="warning">جاري اختبار الاستقرار...</div>';

            // Test multiple page refreshes
            const adminWindow = window.open('index.html', '_blank');
            let refreshCount = 0;
            const maxRefreshes = 3;

            const refreshTest = setInterval(() => {
                if (refreshCount < maxRefreshes) {
                    adminWindow.location.reload();
                    refreshCount++;
                } else {
                    clearInterval(refreshTest);
                    
                    setTimeout(() => {
                        try {
                            const dashboard = adminWindow.document.getElementById('dashboard');
                            const enhancedDashboard = dashboard ? dashboard.querySelector('.dashboard-header') : null;
                            
                            if (enhancedDashboard) {
                                resultDiv.innerHTML = '<div class="success">✅ لوحة التحكم مستقرة بعد ' + maxRefreshes + ' تحديثات</div>';
                                testResults.stability = true;
                            } else {
                                resultDiv.innerHTML = '<div class="error">❌ لوحة التحكم غير مستقرة</div>';
                            }
                        } catch (error) {
                            resultDiv.innerHTML = '<div class="error">❌ خطأ في اختبار الاستقرار: ' + error.message + '</div>';
                        }
                    }, 2000);
                }
            }, 3000);
        }

        function testThemeToggle() {
            const resultDiv = document.getElementById('themeTestResult');
            resultDiv.innerHTML = '<div class="warning">جاري اختبار تغيير المظهر...</div>';

            const adminWindow = window.open('index.html', '_blank');
            
            setTimeout(() => {
                try {
                    const themeButton = adminWindow.document.getElementById('themeToggleBtn');
                    if (themeButton && adminWindow.themeManager) {
                        themeButton.click();
                        
                        setTimeout(() => {
                            const currentTheme = adminWindow.document.documentElement.getAttribute('data-theme');
                            if (currentTheme === 'dark') {
                                resultDiv.innerHTML = '<div class="success">✅ تغيير المظهر يعمل بشكل صحيح</div>';
                                testResults.theme = true;
                            } else {
                                resultDiv.innerHTML = '<div class="error">❌ تغيير المظهر لا يعمل</div>';
                            }
                        }, 1000);
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ زر تغيير المظهر غير موجود</div>';
                    }
                } catch (error) {
                    resultDiv.innerHTML = '<div class="error">❌ خطأ في اختبار المظهر: ' + error.message + '</div>';
                }
            }, 3000);
        }

        function runAllTests() {
            const summaryDiv = document.getElementById('summaryResult');
            summaryDiv.innerHTML = '<div class="warning">جاري تشغيل جميع الاختبارات...</div>';

            // Reset results
            testResults = { protection: false, navigation: 0, stability: false, theme: false };

            // Run all tests
            testDashboardProtection();
            setTimeout(() => testStability(), 1000);
            setTimeout(() => testThemeToggle(), 2000);

            // Show summary after all tests
            setTimeout(() => {
                const totalTests = 4;
                const passedTests = (testResults.protection ? 1 : 0) + 
                                  (testResults.navigation > 10 ? 1 : 0) + 
                                  (testResults.stability ? 1 : 0) + 
                                  (testResults.theme ? 1 : 0);

                const percentage = Math.round((passedTests / totalTests) * 100);
                
                if (percentage >= 75) {
                    summaryDiv.innerHTML = `<div class="success">🎉 نجح ${passedTests} من ${totalTests} اختبارات (${percentage}%)</div>`;
                } else {
                    summaryDiv.innerHTML = `<div class="error">⚠️ نجح ${passedTests} من ${totalTests} اختبارات (${percentage}%)</div>`;
                }
            }, 10000);
        }
    </script>
</body>
</html>
