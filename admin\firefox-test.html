<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🦊 Firefox Compatibility Test</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .test-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        max-width: 800px;
        margin: 0 auto;
      }

      .test-result {
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
        border-left: 4px solid;
      }

      .test-success {
        background: #d4edda;
        border-color: #28a745;
        color: #155724;
      }

      .test-error {
        background: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
      }

      .test-warning {
        background: #fff3cd;
        border-color: #ffc107;
        color: #856404;
      }

      .browser-info {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1 class="text-center mb-4">🦊 Firefox Compatibility Test</h1>

      <div id="browser-info" class="browser-info">
        <h5>معلومات المتصفح</h5>
        <div id="browser-details"></div>
      </div>

      <div class="text-center mb-4">
        <button id="setup-roles" class="btn btn-success btn-lg me-2">
          🔧 إعداد الأدوار
        </button>
        <button id="run-tests" class="btn btn-primary btn-lg">
          🧪 تشغيل الاختبارات
        </button>
      </div>

      <div id="test-results"></div>
    </div>

    <script src="js/firefox-compatibility.js"></script>
    <script>
      // Display browser information
      function displayBrowserInfo() {
        const userAgent = navigator.userAgent;
        const isFirefox = userAgent.toLowerCase().indexOf("firefox") > -1;
        const browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/);

        document.getElementById("browser-details").innerHTML = `
                <p><strong>User Agent:</strong> ${userAgent}</p>
                <p><strong>Is Firefox:</strong> ${
                  isFirefox ? "✅ Yes" : "❌ No"
                }</p>
                ${
                  browserVersion
                    ? `<p><strong>Firefox Version:</strong> ${browserVersion[1]}</p>`
                    : ""
                }
                <p><strong>Compatibility Mode:</strong> ${
                  window.firefoxCompat ? "✅ Active" : "❌ Inactive"
                }</p>
            `;
      }

      // Test functions
      async function testDatabaseAPI() {
        const result = {
          name: "Database API Test",
          success: false,
          message: "",
        };

        try {
          let data;
          if (
            window.FirefoxCompatibility &&
            navigator.userAgent.toLowerCase().indexOf("firefox") > -1
          ) {
            data = await FirefoxCompatibility.apiCall(
              "../php/api/test-connection.php"
            );
          } else {
            const response = await fetch("../php/api/test-connection.php");
            data = await response.json();
          }

          if (data && data.success) {
            result.success = true;
            result.message = `✅ Database connection successful. DB: ${
              data.database_info?.name || "unknown"
            }`;
            if (data.roles_table) {
              result.message += `, Roles: ${
                data.roles_table.exists
                  ? data.roles_table.count + " found"
                  : "table missing"
              }`;
            }
          } else {
            result.message = `❌ Database connection failed: ${
              data?.message || data?.error || "Unknown error"
            }`;
          }
        } catch (error) {
          result.message = `❌ API call failed: ${error.message}`;
        }

        return result;
      }

      async function testRolesAPI() {
        const result = { name: "Roles API Test", success: false, message: "" };

        try {
          let data;
          if (
            window.FirefoxCompatibility &&
            navigator.userAgent.toLowerCase().indexOf("firefox") > -1
          ) {
            data = await FirefoxCompatibility.apiCall(
              "../php/api/test-roles.php"
            );
          } else {
            const response = await fetch("../php/api/test-roles.php");
            data = await response.json();
          }

          if (data && data.success && Array.isArray(data.roles)) {
            result.success = true;
            result.message = `✅ Roles API working. Found ${data.roles.length} roles`;
          } else {
            result.message = `❌ Roles API failed: ${
              data?.message || "Invalid response format"
            }`;
          }
        } catch (error) {
          result.message = `❌ Roles API call failed: ${error.message}`;
        }

        return result;
      }

      async function testJSONParsing() {
        const result = {
          name: "JSON Parsing Test",
          success: false,
          message: "",
        };

        try {
          const testJSON = '{"test": "value", "number": 123, "array": [1,2,3]}';
          const parsed = JSON.parse(testJSON);

          if (
            parsed.test === "value" &&
            parsed.number === 123 &&
            Array.isArray(parsed.array)
          ) {
            result.success = true;
            result.message = "✅ JSON parsing working correctly";
          } else {
            result.message = "❌ JSON parsing returned unexpected values";
          }
        } catch (error) {
          result.message = `❌ JSON parsing failed: ${error.message}`;
        }

        return result;
      }

      async function testFetchAPI() {
        const result = { name: "Fetch API Test", success: false, message: "" };

        try {
          const response = await fetch(
            'data:application/json,{"test":"success"}'
          );
          const data = await response.json();

          if (data.test === "success") {
            result.success = true;
            result.message = "✅ Fetch API working correctly";
          } else {
            result.message = "❌ Fetch API returned unexpected data";
          }
        } catch (error) {
          result.message = `❌ Fetch API failed: ${error.message}`;
        }

        return result;
      }

      // Run all tests
      async function runAllTests() {
        const resultsDiv = document.getElementById("test-results");
        resultsDiv.innerHTML =
          '<div class="text-center"><div class="spinner-border" role="status"></div><p>جاري تشغيل الاختبارات...</p></div>';

        const tests = [
          testJSONParsing,
          testFetchAPI,
          testDatabaseAPI,
          testRolesAPI,
        ];

        const results = [];

        for (const test of tests) {
          try {
            const result = await test();
            results.push(result);
          } catch (error) {
            results.push({
              name: test.name || "Unknown Test",
              success: false,
              message: `❌ Test execution failed: ${error.message}`,
            });
          }
        }

        // Display results
        let html = "<h4>نتائج الاختبارات:</h4>";
        let successCount = 0;

        results.forEach((result) => {
          const cssClass = result.success ? "test-success" : "test-error";
          if (result.success) successCount++;

          html += `
                    <div class="test-result ${cssClass}">
                        <h6>${result.name}</h6>
                        <p>${result.message}</p>
                    </div>
                `;
        });

        // Summary
        const successRate = Math.round((successCount / results.length) * 100);
        const summaryClass =
          successRate >= 75
            ? "test-success"
            : successRate >= 50
            ? "test-warning"
            : "test-error";

        html =
          `
                <div class="test-result ${summaryClass}">
                    <h5>📊 ملخص النتائج</h5>
                    <p>معدل النجاح: ${successRate}% (${successCount}/${results.length})</p>
                </div>
            ` + html;

        resultsDiv.innerHTML = html;
      }

      // Setup roles function
      async function setupRoles() {
        const resultsDiv = document.getElementById("test-results");
        resultsDiv.innerHTML =
          '<div class="text-center"><div class="spinner-border" role="status"></div><p>جاري إعداد جدول الأدوار...</p></div>';

        try {
          const response = await fetch(
            "../php/api/database-setup.php?action=setup_roles"
          );
          const data = await response.json();

          let html = "<h4>نتائج إعداد الأدوار:</h4>";

          if (data.success) {
            html +=
              '<div class="test-result test-success"><h6>✅ تم إعداد الأدوار بنجاح</h6>';
            data.fixes.forEach((fix) => {
              html += `<p>• ${fix}</p>`;
            });
            html += "</div>";
          } else {
            html +=
              '<div class="test-result test-error"><h6>❌ فشل في إعداد الأدوار</h6>';
            html += `<p>${data.message}</p>`;
            if (data.errors) {
              data.errors.forEach((error) => {
                html += `<p>• ${error}</p>`;
              });
            }
            html += "</div>";
          }

          resultsDiv.innerHTML = html;
        } catch (error) {
          resultsDiv.innerHTML = `<div class="test-result test-error">
            <h6>❌ خطأ في الاتصال</h6>
            <p>${error.message}</p>
          </div>`;
        }
      }

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        displayBrowserInfo();

        document
          .getElementById("setup-roles")
          .addEventListener("click", setupRoles);
        document
          .getElementById("run-tests")
          .addEventListener("click", runAllTests);
      });
    </script>
  </body>
</html>
