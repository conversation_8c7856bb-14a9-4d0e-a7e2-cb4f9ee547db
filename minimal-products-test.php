<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-User-Role');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    require_once __DIR__ . '/php/config.php';
    
    if (!isset($conn) || !$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Check if tables exist
    $tables = [];
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    $response = [
        'success' => true,
        'message' => 'Database connection successful',
        'tables' => $tables,
        'has_produits' => in_array('produits', $tables),
        'has_users' => in_array('users', $tables)
    ];
    
    // If produits table exists, get some basic data
    if (in_array('produits', $tables)) {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM produits");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['products_count'] = $count['count'];
        
        // Get first few products
        $stmt = $conn->query("SELECT * FROM produits LIMIT 5");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['sample_products'] = $products;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>