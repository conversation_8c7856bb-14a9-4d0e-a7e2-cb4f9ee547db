<?php
/**
 * Test Roles API
 * Simple test to verify roles table and API functionality
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'poultraydz';
$username = 'root';
$password = 'root';

$results = [
    'success' => false,
    'message' => '',
    'tests' => [],
    'errors' => []
];

try {
    // Create PDO connection
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    $results['tests'][] = [
        'name' => 'Database Connection',
        'status' => 'PASS',
        'message' => 'Database connection successful'
    ];

    // Test 1: Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() > 0) {
        $results['tests'][] = [
            'name' => 'Roles Table Exists',
            'status' => 'PASS',
            'message' => 'Roles table found'
        ];

        // Test 2: Check table structure
        $stmt = $pdo->query("DESCRIBE roles");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = ['id', 'name', 'display_name', 'display_name_ar'];
        $missingColumns = array_diff($requiredColumns, $columnNames);
        
        if (empty($missingColumns)) {
            $results['tests'][] = [
                'name' => 'Table Structure',
                'status' => 'PASS',
                'message' => 'All required columns present: ' . implode(', ', $columnNames)
            ];
        } else {
            $results['tests'][] = [
                'name' => 'Table Structure',
                'status' => 'FAIL',
                'message' => 'Missing columns: ' . implode(', ', $missingColumns)
            ];
        }

        // Test 3: Check if table has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $count = $stmt->fetch()['count'];
        
        if ($count > 0) {
            $results['tests'][] = [
                'name' => 'Table Data',
                'status' => 'PASS',
                'message' => "Found $count roles in table"
            ];

            // Test 4: Get sample roles
            $stmt = $pdo->query("SELECT id, name, display_name, display_name_ar FROM roles LIMIT 5");
            $roles = $stmt->fetchAll();
            
            $results['tests'][] = [
                'name' => 'Sample Roles',
                'status' => 'PASS',
                'message' => 'Sample roles retrieved',
                'data' => $roles
            ];
        } else {
            $results['tests'][] = [
                'name' => 'Table Data',
                'status' => 'FAIL',
                'message' => 'Roles table is empty'
            ];
        }

        // Test 5: Test API query format
        try {
            $stmt = $pdo->prepare("
                SELECT
                    r.*,
                    0 as user_count
                FROM roles r
                ORDER BY r.id ASC
                LIMIT 3
            ");
            $stmt->execute();
            $apiRoles = $stmt->fetchAll();

            // Format like the API does
            $formattedRoles = array_map(function ($role) {
                return [
                    'id' => (int)$role['id'],
                    'name' => $role['name'],
                    'display_name' => $role['display_name'] ?? $role['name'],
                    'display_name_ar' => $role['display_name_ar'] ?? $role['name'],
                    'description' => $role['description'] ?? '',
                    'permissions' => json_decode($role['permissions'] ?? '[]', true),
                    'is_active' => (bool)($role['is_active'] ?? 1),
                    'user_count' => 0,
                    'created_at' => $role['created_at'] ?? null,
                    'updated_at' => $role['updated_at'] ?? null
                ];
            }, $apiRoles);

            $results['tests'][] = [
                'name' => 'API Format Test',
                'status' => 'PASS',
                'message' => 'API query and formatting successful',
                'data' => $formattedRoles
            ];

        } catch (Exception $e) {
            $results['tests'][] = [
                'name' => 'API Format Test',
                'status' => 'FAIL',
                'message' => 'API query failed: ' . $e->getMessage()
            ];
        }

    } else {
        $results['tests'][] = [
            'name' => 'Roles Table Exists',
            'status' => 'FAIL',
            'message' => 'Roles table not found'
        ];
    }

    // Overall result
    $passedTests = array_filter($results['tests'], function($test) {
        return $test['status'] === 'PASS';
    });
    
    $totalTests = count($results['tests']);
    $passedCount = count($passedTests);
    
    $results['success'] = $passedCount === $totalTests;
    $results['message'] = $results['success'] 
        ? "All tests passed ($passedCount/$totalTests)"
        : "Some tests failed ($passedCount/$totalTests)";
    
    $results['summary'] = [
        'total' => $totalTests,
        'passed' => $passedCount,
        'failed' => $totalTests - $passedCount,
        'success_rate' => round(($passedCount / $totalTests) * 100, 2) . '%'
    ];

} catch (Exception $e) {
    $results['success'] = false;
    $results['message'] = 'Test failed: ' . $e->getMessage();
    $results['errors'][] = $e->getMessage();
}

echo json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
