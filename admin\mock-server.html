<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Mock Server - حل مؤقت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .mock-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="mock-container">
        <h1 class="text-center mb-4">🔧 Mock Server - حل مؤقت للاختبار</h1>
        
        <div class="status-warning">
            <h5>⚠️ مشكلة الخادم الحالية</h5>
            <p>خادمك الحالي لا يعالج ملفات PHP. هذا يسبب جميع الأخطاء التي تراها.</p>
        </div>
        
        <div class="text-center mb-4">
            <button id="start-mock" class="btn btn-success btn-lg me-2">🚀 تشغيل Mock Server</button>
            <button id="test-apis" class="btn btn-primary btn-lg">🧪 اختبار APIs</button>
        </div>
        
        <div id="results"></div>
        
        <div class="status-success">
            <h5>✅ الحل النهائي</h5>
            <p>لحل المشكلة نهائياً، يجب تشغيل خادم PHP:</p>
            <div class="code-block">
# في Terminal/Command Prompt:
# 1. اذهب إلى مجلد المشروع
cd K:\Projets_Sites_Web\Mossaab-LandingPage-18-Jul-25

# 2. أوقف الخادم الحالي (Ctrl+C)

# 3. شغل خادم PHP
php -S localhost:8000

# 4. اختبر في المتصفح
http://localhost:8000/admin/firefox-test.html
            </div>
        </div>
    </div>

    <script>
        // Mock API responses for testing
        const mockResponses = {
            'test-connection': {
                success: true,
                message: 'Database connection successful (MOCK)',
                timestamp: new Date().toISOString(),
                database_info: {
                    name: 'mossab-landing-page',
                    version: '10.5.8-MariaDB',
                    host: 'localhost',
                    port: '3307'
                },
                test_query: {
                    test: 1,
                    timestamp: new Date().toISOString()
                },
                roles_table: {
                    exists: true,
                    count: 5
                }
            },
            'test-roles': {
                success: true,
                message: 'Roles retrieved successfully (MOCK)',
                roles: [
                    { id: 1, name: 'admin', display_name: 'Administrator', display_name_ar: 'مدير النظام' },
                    { id: 2, name: 'seller', display_name: 'Seller', display_name_ar: 'بائع' },
                    { id: 3, name: 'user', display_name: 'User', display_name_ar: 'مستخدم' },
                    { id: 4, name: 'moderator', display_name: 'Moderator', display_name_ar: 'مشرف' },
                    { id: 5, name: 'editor', display_name: 'Editor', display_name_ar: 'محرر' }
                ],
                total: 5,
                table_exists: true,
                timestamp: new Date().toISOString()
            }
        };
        
        let mockServerActive = false;
        
        // Override fetch for mock responses
        const originalFetch = window.fetch;
        
        function startMockServer() {
            mockServerActive = true;
            
            // Override fetch globally
            window.fetch = function(url, options = {}) {
                if (!mockServerActive) {
                    return originalFetch(url, options);
                }
                
                // Check if this is an API call we can mock
                if (url.includes('test-connection.php')) {
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        headers: new Headers({ 'content-type': 'application/json' }),
                        json: () => Promise.resolve(mockResponses['test-connection']),
                        text: () => Promise.resolve(JSON.stringify(mockResponses['test-connection']))
                    });
                }
                
                if (url.includes('test-roles.php')) {
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        headers: new Headers({ 'content-type': 'application/json' }),
                        json: () => Promise.resolve(mockResponses['test-roles']),
                        text: () => Promise.resolve(JSON.stringify(mockResponses['test-roles']))
                    });
                }
                
                // For other APIs, return a generic success response
                const genericResponse = {
                    success: true,
                    message: 'Mock response - PHP server needed for real data',
                    data: [],
                    timestamp: new Date().toISOString()
                };
                
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    headers: new Headers({ 'content-type': 'application/json' }),
                    json: () => Promise.resolve(genericResponse),
                    text: () => Promise.resolve(JSON.stringify(genericResponse))
                });
            };
            
            document.getElementById('results').innerHTML = `
                <div class="status-success">
                    <h5>✅ Mock Server Active</h5>
                    <p>Les APIs sont maintenant mockées et retournent des données de test.</p>
                    <p>Vous pouvez maintenant tester Firefox compatibility sans erreurs.</p>
                    <a href="firefox-test.html" class="btn btn-success" target="_blank">🦊 Test Firefox</a>
                </div>
            `;
        }
        
        async function testAPIs() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>Testing APIs...</p></div>';
            
            const tests = [
                { name: 'Database Connection', url: '../php/api/test-connection.php' },
                { name: 'Roles API', url: '../php/api/test-roles.php' }
            ];
            
            let html = '<h4>نتائج اختبار APIs:</h4>';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const data = await response.json();
                    
                    const status = data.success ? '✅' : '❌';
                    const cssClass = data.success ? 'status-success' : 'status-warning';
                    
                    html += `
                        <div class="${cssClass}">
                            <h6>${status} ${test.name}</h6>
                            <p>${data.message}</p>
                            ${data.success && mockServerActive ? '<small>(Mock Response)</small>' : ''}
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="status-warning">
                            <h6>⚠️ ${test.name}</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            results.innerHTML = html;
        }
        
        document.getElementById('start-mock').addEventListener('click', startMockServer);
        document.getElementById('test-apis').addEventListener('click', testAPIs);
        
        // Show initial status
        document.getElementById('results').innerHTML = `
            <div class="status-warning">
                <h5>📊 État actuel</h5>
                <p>Mock Server: <strong>Inactif</strong></p>
                <p>Cliquez sur "تشغيل Mock Server" pour activer les réponses de test.</p>
            </div>
        `;
    </script>
</body>
</html>
