<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح التوجيهات - متجر مصعب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
        }
        
        .section h3 {
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح التوجيهات</h1>
            <p>حل مشكلة التوجيه إلى firebase-login.html</p>
        </div>

        <!-- Problem Description -->
        <div class="section">
            <h3>🔍 المشكلة</h3>
            <div class="status error">
                <strong>المشكلة:</strong> admin/login.html يتم توجيهه إلى admin/firebase-login.html بدلاً من البقاء في admin/login.html
            </div>
        </div>

        <!-- Solution -->
        <div class="section">
            <h3>✅ الحل</h3>
            <div id="fixStatus" class="status info">جاهز لتطبيق الإصلاح</div>
            
            <button class="btn btn-success" onclick="applyFix()">تطبيق الإصلاح</button>
            <button class="btn btn-primary" onclick="testRedirect()">اختبار التوجيه</button>
        </div>

        <!-- Manual Steps -->
        <div class="section">
            <h3>📋 خطوات يدوية (إذا لم يعمل الإصلاح التلقائي)</h3>
            <div class="status info">
                <ol style="padding-right: 20px; margin: 10px 0;">
                    <li>تأكد من أن admin/login.html لا يحتوي على أي إشارة إلى firebase-login.html</li>
                    <li>امسح cache المتصفح (Ctrl+Shift+Delete)</li>
                    <li>امسح localStorage و sessionStorage</li>
                    <li>أعد تحميل الصفحة</li>
                </ol>
            </div>
            
            <button class="btn btn-danger" onclick="clearAllCache()">مسح جميع البيانات المحفوظة</button>
        </div>

        <!-- Test Links -->
        <div class="section">
            <h3>🔗 روابط الاختبار</h3>
            <a href="login.html" class="btn btn-primary" target="_blank">admin/login.html</a>
            <a href="index.html" class="btn btn-primary" target="_blank">admin/index.html</a>
            <a href="../login.html" class="btn btn-primary" target="_blank">login.html (المستخدمين)</a>
        </div>

        <!-- Current Status -->
        <div class="section">
            <h3>📊 الحالة الحالية</h3>
            <div id="currentStatus">جاري فحص الحالة...</div>
        </div>
    </div>

    <script>
        // Apply fix
        function applyFix() {
            const statusDiv = document.getElementById('fixStatus');
            
            // Clear all redirect-related storage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear specific redirect counters
            sessionStorage.removeItem('auth_redirect_count');
            localStorage.removeItem('firebase_auth_state');
            
            // Clear any cached redirects
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            statusDiv.className = 'status success';
            statusDiv.innerHTML = '✅ تم تطبيق الإصلاح بنجاح!<br>جرب الآن الوصول إلى admin/login.html';
        }

        // Test redirect
        function testRedirect() {
            const statusDiv = document.getElementById('fixStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 جاري اختبار التوجيه...';
            
            // Test if login.html loads correctly
            fetch('login.html')
                .then(response => {
                    if (response.ok) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = '✅ admin/login.html يعمل بشكل صحيح';
                        
                        // Open in new tab for testing
                        setTimeout(() => {
                            window.open('login.html', '_blank');
                        }, 1000);
                    } else {
                        throw new Error('Failed to load login.html');
                    }
                })
                .catch(error => {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ خطأ في تحميل admin/login.html: ' + error.message;
                });
        }

        // Clear all cache
        function clearAllCache() {
            // Clear storage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear cookies
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            // Clear cache if available
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            alert('تم مسح جميع البيانات المحفوظة. سيتم إعادة تحميل الصفحة.');
            
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        }

        // Check current status
        function checkCurrentStatus() {
            const statusDiv = document.getElementById('currentStatus');
            
            const status = {
                currentUrl: window.location.href,
                localStorage: Object.keys(localStorage).length + ' items',
                sessionStorage: Object.keys(sessionStorage).length + ' items',
                redirectCount: sessionStorage.getItem('auth_redirect_count') || '0',
                userAgent: navigator.userAgent.includes('Chrome') ? 'Chrome' : 
                          navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other'
            };
            
            statusDiv.innerHTML = Object.entries(status)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
        }

        // Initialize
        setTimeout(checkCurrentStatus, 500);

        // Auto-fix on load if needed
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('autofix') === 'true') {
            setTimeout(applyFix, 1000);
        }
    </script>
</body>
</html>
