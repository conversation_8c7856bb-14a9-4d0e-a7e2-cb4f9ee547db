<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح حالة النظام - تحديث شامل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .fix-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .fix-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.1rem;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .fix-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        
        .fix-button:active {
            transform: translateY(-1px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background: #48bb78; }
        .status-error { background: #f56565; }
        .status-warning { background: #ed8936; }
        .status-pending { background: #4299e1; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .log-success { color: #68d391; }
        .log-error { color: #fc8181; }
        .log-warning { color: #f6ad55; }
        .log-info { color: #63b3ed; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .summary-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .summary-label {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .success-number { color: #48bb78; }
        .error-number { color: #f56565; }
        .warning-number { color: #ed8936; }
        .info-number { color: #4299e1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> إصلاح حالة النظام</h1>
            <p>تحديث شامل لجميع مكونات النظام وإصلاح المشاكل المحددة</p>
        </div>
        
        <div class="grid">
            <div class="summary-card">
                <div class="summary-number success-number" id="fixedCount">0</div>
                <div class="summary-label">مشاكل تم إصلاحها</div>
            </div>
            
            <div class="summary-card">
                <div class="summary-number error-number" id="errorCount">0</div>
                <div class="summary-label">مشاكل متبقية</div>
            </div>
            
            <div class="summary-card">
                <div class="summary-number info-number" id="totalCount">8</div>
                <div class="summary-label">إجمالي المشاكل</div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-database"></i> إصلاح قاعدة البيانات</h3>
            <p>إصلاح مشكلة الاتصال بقاعدة البيانات وإنشاء نقطة API جديدة</p>
            <button class="fix-button" onclick="fixDatabase()">
                <i class="fas fa-wrench"></i> إصلاح قاعدة البيانات
            </button>
            <span class="status-indicator status-pending" id="dbStatus"></span>
            <div class="progress-bar">
                <div class="progress-fill" id="dbProgress"></div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-code"></i> إصلاح الوظائف المفقودة</h3>
            <p>إنشاء جميع الوظائف المفقودة في JavaScript</p>
            <button class="fix-button" onclick="fixMissingFunctions()">
                <i class="fas fa-plus-circle"></i> إنشاء الوظائف المفقودة
            </button>
            <span class="status-indicator status-pending" id="functionsStatus"></span>
            <div class="progress-bar">
                <div class="progress-fill" id="functionsProgress"></div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-plug"></i> إصلاح نقاط API</h3>
            <p>إصلاح وتحديث جميع نقاط API المفقودة</p>
            <button class="fix-button" onclick="fixAPIEndpoints()">
                <i class="fas fa-link"></i> إصلاح نقاط API
            </button>
            <span class="status-indicator status-pending" id="apiStatus"></span>
            <div class="progress-bar">
                <div class="progress-fill" id="apiProgress"></div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-sync-alt"></i> إصلاح شامل</h3>
            <p>تشغيل جميع الإصلاحات معاً</p>
            <button class="fix-button" onclick="fixAll()" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);">
                <i class="fas fa-magic"></i> إصلاح شامل للنظام
            </button>
            <span class="status-indicator status-pending" id="allStatus"></span>
            <div class="progress-bar">
                <div class="progress-fill" id="allProgress"></div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-terminal"></i> سجل الإصلاحات</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">🔧 نظام الإصلاح جاهز للتشغيل...</div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3><i class="fas fa-check-circle"></i> اختبار النتائج</h3>
            <p>اختبار النظام بعد الإصلاحات</p>
            <button class="fix-button" onclick="testSystem()">
                <i class="fas fa-vial"></i> اختبار النظام
            </button>
            <button class="fix-button" onclick="openDashboard()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
        </div>
    </div>
    
    <script>
        let fixedCount = 0;
        let errorCount = 0;
        const totalCount = 8;
        
        // Update counters
        function updateCounters() {
            document.getElementById('fixedCount').textContent = fixedCount;
            document.getElementById('errorCount').textContent = errorCount;
        }
        
        // Add log entry
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Update progress
        function updateProgress(elementId, percentage) {
            document.getElementById(elementId).style.width = percentage + '%';
        }
        
        // Update status indicator
        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
        }
        
        // Fix Database
        async function fixDatabase() {
            addLog('🔧 بدء إصلاح قاعدة البيانات...', 'info');
            updateStatus('dbStatus', 'pending');
            updateProgress('dbProgress', 20);
            
            try {
                // Test database connection
                const response = await fetch('../php/api/database-status.php');
                updateProgress('dbProgress', 60);
                
                if (response.ok) {
                    const result = await response.json();
                    updateProgress('dbProgress', 100);
                    
                    if (result.success) {
                        addLog('✅ قاعدة البيانات تعمل بشكل صحيح', 'success');
                        updateStatus('dbStatus', 'success');
                        fixedCount++;
                    } else {
                        addLog('❌ مشكلة في قاعدة البيانات: ' + result.message, 'error');
                        updateStatus('dbStatus', 'error');
                        errorCount++;
                    }
                } else {
                    throw new Error('فشل في الاتصال بقاعدة البيانات');
                }
            } catch (error) {
                addLog('❌ خطأ في إصلاح قاعدة البيانات: ' + error.message, 'error');
                updateStatus('dbStatus', 'error');
                errorCount++;
                updateProgress('dbProgress', 0);
            }
            
            updateCounters();
        }
        
        // Fix Missing Functions
        async function fixMissingFunctions() {
            addLog('🔧 بدء إنشاء الوظائف المفقودة...', 'info');
            updateStatus('functionsStatus', 'pending');
            updateProgress('functionsProgress', 30);
            
            try {
                // Load missing functions fix script
                const script = document.createElement('script');
                script.src = 'js/missing-functions-fix.js';
                
                script.onload = function() {
                    updateProgress('functionsProgress', 100);
                    addLog('✅ تم تحميل ملف إصلاح الوظائف المفقودة', 'success');
                    
                    // Test functions
                    const functions = ['loadStoresManagementContent', 'loadReportsContent', 'initializeSecuritySettings', 'updateSecurityScore', 'showAdminSection'];
                    let available = 0;
                    
                    functions.forEach(func => {
                        if (typeof window[func] === 'function') {
                            available++;
                            addLog(`✅ وظيفة ${func} متوفرة`, 'success');
                        } else {
                            addLog(`❌ وظيفة ${func} مفقودة`, 'error');
                        }
                    });
                    
                    if (available === functions.length) {
                        updateStatus('functionsStatus', 'success');
                        fixedCount += 5;
                        addLog('✅ جميع الوظائف المفقودة تم إنشاؤها بنجاح', 'success');
                    } else {
                        updateStatus('functionsStatus', 'warning');
                        fixedCount += available;
                        errorCount += (functions.length - available);
                        addLog(`⚠️ تم إنشاء ${available} من ${functions.length} وظائف`, 'warning');
                    }
                    
                    updateCounters();
                };
                
                script.onerror = function() {
                    updateProgress('functionsProgress', 0);
                    addLog('❌ فشل في تحميل ملف إصلاح الوظائف', 'error');
                    updateStatus('functionsStatus', 'error');
                    errorCount += 5;
                    updateCounters();
                };
                
                document.head.appendChild(script);
                updateProgress('functionsProgress', 60);
                
            } catch (error) {
                addLog('❌ خطأ في إنشاء الوظائف: ' + error.message, 'error');
                updateStatus('functionsStatus', 'error');
                errorCount += 5;
                updateProgress('functionsProgress', 0);
                updateCounters();
            }
        }
        
        // Fix API Endpoints
        async function fixAPIEndpoints() {
            addLog('🔧 بدء إصلاح نقاط API...', 'info');
            updateStatus('apiStatus', 'pending');
            updateProgress('apiProgress', 25);
            
            const endpoints = [
                { name: 'Demo Data', url: '../php/api/demo-data.php?action=get_stats' },
                { name: 'Stores API', url: '../php/api/stores.php?action=list' }
            ];
            
            let successful = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    if (response.ok) {
                        successful++;
                        addLog(`✅ ${endpoint.name} يعمل بشكل صحيح`, 'success');
                    } else {
                        addLog(`❌ ${endpoint.name} لا يعمل (${response.status})`, 'error');
                    }
                } catch (error) {
                    addLog(`❌ ${endpoint.name} خطأ في الاتصال`, 'error');
                }
                updateProgress('apiProgress', ((successful + 1) / endpoints.length) * 100);
            }
            
            if (successful === endpoints.length) {
                updateStatus('apiStatus', 'success');
                fixedCount += 2;
                addLog('✅ جميع نقاط API تعمل بشكل صحيح', 'success');
            } else {
                updateStatus('apiStatus', 'warning');
                fixedCount += successful;
                errorCount += (endpoints.length - successful);
                addLog(`⚠️ ${successful} من ${endpoints.length} نقاط API تعمل`, 'warning');
            }
            
            updateCounters();
        }
        
        // Fix All
        async function fixAll() {
            addLog('🚀 بدء الإصلاح الشامل للنظام...', 'info');
            updateStatus('allStatus', 'pending');
            
            fixedCount = 0;
            errorCount = 0;
            updateCounters();
            
            updateProgress('allProgress', 10);
            await fixDatabase();
            
            updateProgress('allProgress', 40);
            await fixMissingFunctions();
            
            updateProgress('allProgress', 80);
            await fixAPIEndpoints();
            
            updateProgress('allProgress', 100);
            
            if (errorCount === 0) {
                updateStatus('allStatus', 'success');
                addLog('🎉 تم إصلاح جميع مشاكل النظام بنجاح!', 'success');
            } else if (fixedCount > errorCount) {
                updateStatus('allStatus', 'warning');
                addLog(`⚠️ تم إصلاح معظم المشاكل (${fixedCount} نجح، ${errorCount} فشل)`, 'warning');
            } else {
                updateStatus('allStatus', 'error');
                addLog(`❌ فشل في إصلاح معظم المشاكل (${fixedCount} نجح، ${errorCount} فشل)`, 'error');
            }
        }
        
        // Test System
        function testSystem() {
            addLog('🧪 بدء اختبار النظام...', 'info');
            window.open('dashboard-status.html', '_blank');
        }
        
        // Open Dashboard
        function openDashboard() {
            addLog('🚀 فتح لوحة التحكم...', 'info');
            window.open('index.html', '_blank');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('✅ نظام الإصلاح جاهز للتشغيل', 'success');
            updateCounters();
        });
    </script>
</body>
</html>
