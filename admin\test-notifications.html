<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 اختبار الإشعارات</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 50px auto;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn-success { background: #28a745; color: white; }
        .btn-error { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-clear { background: #6c757d; color: white; }
        
        .position-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="notifications-container" id="notificationsContainer"></div>
    
    <div class="test-container">
        <h1 class="text-center">🔔 اختبار نظام الإشعارات</h1>
        
        <div class="position-info">
            <h5>معلومات الموضع:</h5>
            <p><strong>الموضع الحالي:</strong> أعلى يمين الصفحة</p>
            <p><strong>Z-Index:</strong> 9999</p>
            <p><strong>العرض الأقصى:</strong> 350px</p>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn btn-success" onclick="showSuccessNotification()">
                <i class="fas fa-check"></i> إشعار نجاح
            </button>
            <button class="test-btn btn-error" onclick="showErrorNotification()">
                <i class="fas fa-times"></i> إشعار خطأ
            </button>
            <button class="test-btn btn-warning" onclick="showWarningNotification()">
                <i class="fas fa-exclamation-triangle"></i> إشعار تحذير
            </button>
            <button class="test-btn btn-info" onclick="showInfoNotification()">
                <i class="fas fa-info"></i> إشعار معلومات
            </button>
            <button class="test-btn btn-clear" onclick="clearNotifications()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>
        
        <div class="position-info">
            <h5>تعليمات الاختبار:</h5>
            <ul>
                <li>اضغط على الأزرار لإظهار أنواع مختلفة من الإشعارات</li>
                <li>تحقق من أن الإشعارات تظهر في أعلى يمين الصفحة</li>
                <li>تأكد من أن الإشعارات تنزلق من اليمين إلى اليسار</li>
                <li>تحقق من أن الإشعارات تختفي تلقائياً بعد 5 ثوانٍ</li>
            </ul>
        </div>
    </div>

    <script>
        // Simple notification system for testing
        let notificationId = 1;
        
        function createNotification(message, type) {
            const container = document.getElementById('notificationsContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.id = `notification-${notificationId++}`;
            
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="${icons[type]}" style="font-size: 1.2em;"></i>
                    <div>
                        <div style="font-weight: 600; margin-bottom: 5px;">${getTypeTitle(type)}</div>
                        <div style="color: #666; font-size: 0.9em;">${message}</div>
                    </div>
                </div>
                <button onclick="removeNotification('${notification.id}')" style="background: none; border: none; color: #999; cursor: pointer; padding: 5px; font-size: 1.2em; margin-right: 10px;">
                    &times;
                </button>
            `;
            
            container.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                removeNotification(notification.id);
            }, 5000);
            
            return notification;
        }
        
        function getTypeTitle(type) {
            const titles = {
                success: 'نجح العمل',
                error: 'حدث خطأ',
                warning: 'تحذير',
                info: 'معلومات'
            };
            return titles[type] || 'إشعار';
        }
        
        function removeNotification(id) {
            const notification = document.getElementById(id);
            if (notification) {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }
        
        function showSuccessNotification() {
            createNotification('تم حفظ البيانات بنجاح!', 'success');
        }
        
        function showErrorNotification() {
            createNotification('فشل في الاتصال بقاعدة البيانات', 'error');
        }
        
        function showWarningNotification() {
            createNotification('يرجى التحقق من البيانات المدخلة', 'warning');
        }
        
        function showInfoNotification() {
            createNotification('تم تحديث النظام إلى الإصدار الجديد', 'info');
        }
        
        function clearNotifications() {
            const container = document.getElementById('notificationsContainer');
            container.innerHTML = '';
        }
        
        // Add slideOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Show welcome notification on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                showInfoNotification();
            }, 1000);
        });
    </script>
</body>
</html>
