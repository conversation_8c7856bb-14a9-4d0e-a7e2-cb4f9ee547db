<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - Firebase - متجر مصعب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .register-header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }
        
        .register-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .register-content {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .register-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            font-family: inherit;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .register-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .google-button {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            color: #333;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            font-family: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .google-button:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #6c757d;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>📝 إنشاء حساب جديد</h1>
            <p>انضم إلى متجر مصعب للكتب - Firebase</p>
        </div>
        
        <div class="register-content">
            <!-- Messages -->
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <!-- Registration Form -->
            <form id="registerForm">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <input type="text" id="fullName" required>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" required minlength="6">
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                    <input type="password" id="confirmPassword" required>
                </div>
                
                <button type="submit" class="register-button">
                    <i class="fas fa-user-plus"></i> إنشاء الحساب
                </button>
            </form>
            
            <!-- Divider -->
            <div class="divider">
                <span>أو</span>
            </div>
            
            <!-- Google Registration -->
            <button class="google-button" id="googleRegisterBtn">
                <i class="fab fa-google"></i>
                التسجيل باستخدام Google
            </button>
            
            <!-- Links -->
            <div class="links">
                <a href="firebase-login.html">لديك حساب بالفعل؟ سجل الدخول</a>
                <br><br>
                <a href="index.html">العودة إلى الموقع</a>
                <br>
                <a href="admin/firebase-login.html">تسجيل دخول المديرين</a>
            </div>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="admin/js/firebase-config.js"></script>

    <!-- Main Script -->
    <script type="module">
        // Wait for Firebase to initialize
        setTimeout(() => {
            console.log('Firebase initialized for registration');
        }, 1000);
        
        // Password strength checker
        document.getElementById('password').addEventListener('input', (e) => {
            const password = e.target.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            if (strength < 3) {
                strengthDiv.textContent = 'كلمة مرور ضعيفة';
                strengthDiv.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthDiv.textContent = 'كلمة مرور متوسطة';
                strengthDiv.className = 'password-strength strength-medium';
            } else {
                strengthDiv.textContent = 'كلمة مرور قوية';
                strengthDiv.className = 'password-strength strength-strong';
            }
        });
        
        // Registration Form
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // Validation
            if (password !== confirmPassword) {
                showError('كلمات المرور غير متطابقة');
                return;
            }
            
            if (password.length < 6) {
                showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }
            
            setLoading(true);
            clearMessages();
            
            const result = await window.firebaseAuth.signUpWithEmail(email, password, fullName, 'user');
            
            if (result.success) {
                showSuccess('تم إنشاء الحساب بنجاح! جاري التوجيه...');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                showError(result.error);
            }
            
            setLoading(false);
        });
        
        // Google Registration
        document.getElementById('googleRegisterBtn').addEventListener('click', async () => {
            setLoading(true);
            clearMessages();
            
            const result = await window.firebaseAuth.signInWithGoogle();
            
            if (result.success) {
                showSuccess('تم التسجيل بـ Google بنجاح! جاري التوجيه...');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                showError(result.error);
            }
            
            setLoading(false);
        });
        
        // Utility functions
        function setLoading(loading) {
            const registerBtn = document.querySelector('.register-button');
            const googleBtn = document.getElementById('googleRegisterBtn');
            
            registerBtn.disabled = loading;
            googleBtn.disabled = loading;
            
            if (loading) {
                registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
                googleBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التسجيل...';
            } else {
                registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> إنشاء الحساب';
                googleBtn.innerHTML = '<i class="fab fa-google"></i> التسجيل باستخدام Google';
            }
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
        
        function clearMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
        
        // Check if user is already signed in
        window.onFirebaseUserSignedIn = (user, profile) => {
            console.log('User already signed in, redirecting...');
            window.location.href = 'index.html';
        };
    </script>
</body>
</html>
