<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase - متجر مصعب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
        }
        
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار Firebase</h1>
            <p>صفحة تشخيص وإصلاح مشاكل Firebase</p>
        </div>

        <!-- Firebase Status -->
        <div class="test-section">
            <h3>1. حالة Firebase</h3>
            <div id="firebaseStatus" class="status info">جاري فحص Firebase...</div>
            <button class="btn btn-primary" onclick="checkFirebaseStatus()">إعادة فحص Firebase</button>
        </div>

        <!-- Authentication Test -->
        <div class="test-section">
            <h3>2. اختبار المصادقة</h3>
            <div id="authStatus" class="status info">جاري فحص المصادقة...</div>
            <button class="btn btn-success" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <button class="btn btn-danger" onclick="testLogout()">اختبار تسجيل الخروج</button>
        </div>

        <!-- Quick Login -->
        <div class="test-section">
            <h3>3. تسجيل دخول سريع</h3>
            <button class="btn btn-primary" onclick="quickLogin('<EMAIL>', 'Admin123!@#')">تسجيل دخول كمدير</button>
            <button class="btn btn-primary" onclick="quickLogin('<EMAIL>', 'Demo123!')">تسجيل دخول تجريبي</button>
        </div>

        <!-- Console Log -->
        <div class="test-section">
            <h3>4. سجل الأحداث</h3>
            <div id="consoleLog" class="log">جاري تحميل السجل...</div>
            <button class="btn btn-primary" onclick="clearLog()">مسح السجل</button>
        </div>

        <!-- Navigation -->
        <div class="test-section">
            <h3>5. التنقل</h3>
            <button class="btn btn-primary" onclick="window.location.href='login.html'">صفحة تسجيل الدخول</button>
            <button class="btn btn-primary" onclick="window.location.href='index.html'">لوحة التحكم</button>
            <button class="btn btn-primary" onclick="window.location.href='setup-firebase-admins.html'">إعداد المديرين</button>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <script type="module">
        let logMessages = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        function updateLogDisplay() {
            const logDiv = document.getElementById('consoleLog');
            logDiv.innerHTML = logMessages.slice(-20).join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        window.clearLog = function() {
            logMessages = [];
            updateLogDisplay();
        };

        window.checkFirebaseStatus = function() {
            const statusDiv = document.getElementById('firebaseStatus');
            
            if (typeof window.firebaseAuth === 'undefined') {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Firebase غير محمل - تحقق من الاتصال بالإنترنت';
                addLog('Firebase not loaded', 'error');
                return;
            }

            if (window.firebaseAuth) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ Firebase محمل ومتصل بنجاح';
                addLog('Firebase loaded successfully', 'success');
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Firebase محمل لكن غير مهيأ';
                addLog('Firebase loaded but not initialized', 'error');
            }
        };

        window.testLogin = async function() {
            const statusDiv = document.getElementById('authStatus');
            
            if (!window.firebaseAuth) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Firebase غير متوفر';
                addLog('Firebase not available for auth test', 'error');
                return;
            }

            try {
                const result = await window.firebaseAuth.signInWithEmail('<EMAIL>', 'Demo123!');
                
                if (result.success) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ تم تسجيل الدخول بنجاح';
                    addLog('Login test successful', 'success');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `❌ فشل تسجيل الدخول: ${result.error}`;
                    addLog(`Login test failed: ${result.error}`, 'error');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
                addLog(`Login test error: ${error.message}`, 'error');
            }
        };

        window.testLogout = async function() {
            const statusDiv = document.getElementById('authStatus');
            
            if (!window.firebaseAuth) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Firebase غير متوفر';
                addLog('Firebase not available for logout test', 'error');
                return;
            }

            try {
                const result = await window.firebaseAuth.signOutUser();
                
                if (result.success) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ تم تسجيل الخروج بنجاح';
                    addLog('Logout test successful', 'success');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `❌ فشل تسجيل الخروج: ${result.error}`;
                    addLog(`Logout test failed: ${result.error}`, 'error');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
                addLog(`Logout test error: ${error.message}`, 'error');
            }
        };

        window.quickLogin = async function(email, password) {
            addLog(`Attempting quick login for ${email}`, 'info');
            
            if (!window.firebaseAuth) {
                addLog('Firebase not available for quick login', 'error');
                return;
            }

            try {
                const result = await window.firebaseAuth.signInWithEmail(email, password);
                
                if (result.success) {
                    addLog(`Quick login successful for ${email}`, 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    addLog(`Quick login failed for ${email}: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`Quick login error for ${email}: ${error.message}`, 'error');
            }
        };

        // Initialize
        setTimeout(() => {
            addLog('Firebase test page loaded', 'info');
            checkFirebaseStatus();
            
            // Check current auth status
            if (window.firebaseAuth?.isAuthenticated()) {
                const userInfo = window.firebaseAuth.getCurrentUser();
                addLog(`User already authenticated: ${userInfo.user?.email}`, 'success');
                document.getElementById('authStatus').className = 'status success';
                document.getElementById('authStatus').innerHTML = `✅ مستخدم مسجل: ${userInfo.user?.email}`;
            } else {
                addLog('No user currently authenticated', 'info');
                document.getElementById('authStatus').className = 'status info';
                document.getElementById('authStatus').innerHTML = '📝 لا يوجد مستخدم مسجل حالياً';
            }
        }, 1000);

        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            addLog(args.join(' '), 'info');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addLog(args.join(' '), 'error');
            originalError.apply(console, args);
        };
    </script>
</body>
</html>
