// Firebase Configuration and Authentication
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signInWithPopup, GoogleAuthProvider, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getFirestore, doc, setDoc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAHYP4efj_6z7lodL56YF2_vZfLVRnraBs",
  authDomain: "landingpage-a7491.firebaseapp.com",
  projectId: "landingpage-a7491",
  storageBucket: "landingpage-a7491.firebasestorage.app",
  messagingSenderId: "538587228680",
  appId: "1:538587228680:web:662bc194bf9894634b3fbd",
  measurementId: "G-NXQWCWG5YD"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const analytics = getAnalytics(app);

// Google Auth Provider
const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Firebase Auth Manager
class FirebaseAuthManager {
    constructor() {
        this.currentUser = null;
        this.userProfile = null;
        this.initializeAuthListener();
    }

    // Initialize authentication state listener
    initializeAuthListener() {
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                this.currentUser = user;
                await this.loadUserProfile(user.uid);
                this.onUserSignedIn(user);
            } else {
                this.currentUser = null;
                this.userProfile = null;
                this.onUserSignedOut();
            }
        });
    }

    // Sign in with email and password
    async signInWithEmail(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            console.log('✅ Email sign-in successful:', userCredential.user.email);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('❌ Email sign-in error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign up with email and password
    async signUpWithEmail(email, password, displayName = '', role = 'user') {
        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Create user profile in Firestore
            await this.createUserProfile(user.uid, {
                email: user.email,
                displayName: displayName || user.email.split('@')[0],
                role: role,
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString(),
                isActive: true
            });
            
            console.log('✅ Email sign-up successful:', user.email);
            return { success: true, user: user };
        } catch (error) {
            console.error('❌ Email sign-up error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign in with Google
    async signInWithGoogle() {
        try {
            const result = await signInWithPopup(auth, googleProvider);
            const user = result.user;
            
            // Check if user profile exists, create if not
            const userDoc = await getDoc(doc(db, 'users', user.uid));
            if (!userDoc.exists()) {
                await this.createUserProfile(user.uid, {
                    email: user.email,
                    displayName: user.displayName,
                    photoURL: user.photoURL,
                    role: 'user',
                    provider: 'google',
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString(),
                    isActive: true
                });
            } else {
                // Update last login
                await setDoc(doc(db, 'users', user.uid), {
                    lastLogin: new Date().toISOString()
                }, { merge: true });
            }
            
            console.log('✅ Google sign-in successful:', user.email);
            return { success: true, user: user };
        } catch (error) {
            console.error('❌ Google sign-in error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign out
    async signOutUser() {
        try {
            await signOut(auth);
            console.log('✅ User signed out successfully');
            return { success: true };
        } catch (error) {
            console.error('❌ Sign-out error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Create user profile in Firestore
    async createUserProfile(uid, profileData) {
        try {
            await setDoc(doc(db, 'users', uid), profileData);
            console.log('✅ User profile created:', uid);
        } catch (error) {
            console.error('❌ Error creating user profile:', error);
            throw error;
        }
    }

    // Load user profile from Firestore
    async loadUserProfile(uid) {
        try {
            const userDoc = await getDoc(doc(db, 'users', uid));
            if (userDoc.exists()) {
                this.userProfile = userDoc.data();
                console.log('✅ User profile loaded:', this.userProfile);
            } else {
                console.log('⚠️ No user profile found, creating default...');
                // Create default profile
                const defaultProfile = {
                    email: this.currentUser.email,
                    displayName: this.currentUser.displayName || this.currentUser.email.split('@')[0],
                    role: 'user',
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString(),
                    isActive: true
                };
                await this.createUserProfile(uid, defaultProfile);
                this.userProfile = defaultProfile;
            }
        } catch (error) {
            console.error('❌ Error loading user profile:', error);
        }
    }

    // Check if user is admin
    isAdmin() {
        return this.userProfile && ['admin', 'super_admin', 'owner'].includes(this.userProfile.role);
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Get current user info
    getCurrentUser() {
        return {
            user: this.currentUser,
            profile: this.userProfile,
            isAuthenticated: this.isAuthenticated(),
            isAdmin: this.isAdmin()
        };
    }

    // Handle user signed in
    onUserSignedIn(user) {
        console.log('🔐 User signed in:', user.email);
        // Update UI or redirect as needed
        if (typeof window.onFirebaseUserSignedIn === 'function') {
            window.onFirebaseUserSignedIn(user, this.userProfile);
        }
    }

    // Handle user signed out
    onUserSignedOut() {
        console.log('🔓 User signed out');
        // Update UI or redirect as needed
        if (typeof window.onFirebaseUserSignedOut === 'function') {
            window.onFirebaseUserSignedOut();
        }
    }

    // Get user-friendly error message
    getErrorMessage(error) {
        const errorMessages = {
            'auth/user-not-found': 'لم يتم العثور على المستخدم',
            'auth/wrong-password': 'كلمة المرور غير صحيحة',
            'auth/email-already-in-use': 'البريد الإلكتروني مستخدم بالفعل',
            'auth/weak-password': 'كلمة المرور ضعيفة جداً',
            'auth/invalid-email': 'البريد الإلكتروني غير صالح',
            'auth/too-many-requests': 'محاولات كثيرة جداً، حاول لاحقاً',
            'auth/network-request-failed': 'خطأ في الشبكة',
            'auth/popup-closed-by-user': 'تم إغلاق النافذة المنبثقة',
            'auth/cancelled-popup-request': 'تم إلغاء طلب النافذة المنبثقة'
        };
        
        return errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
    }
}

// Create global instance
window.firebaseAuth = new FirebaseAuthManager();

// Export for module usage
export { FirebaseAuthManager, auth, db, analytics };
