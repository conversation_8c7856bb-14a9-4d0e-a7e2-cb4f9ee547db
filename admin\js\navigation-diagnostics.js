/**
 * Navigation Diagnostics
 * Helps diagnose navigation issues
 */

console.log('🔍 Navigation Diagnostics loading...');

function runNavigationDiagnostics() {
    console.log('🔍 Running navigation diagnostics...');

    const results = {
        timestamp: new Date().toISOString(),
        navItems: [],
        sections: [],
        errors: [],
        warnings: []
    };

    // Check navigation items
    const navSelectors = [
        '.admin-nav ul li',
        '.sidebar ul li',
        'nav ul li',
        '[data-section]'
    ];

    navSelectors.forEach(selector => {
        const items = document.querySelectorAll(selector);
        console.log(`Found ${items.length} items for selector: ${selector}`);

        items.forEach((item, index) => {
            const text = item.textContent.trim();

            // Skip items that are containers with concatenated text (like admin settings menu)
            if (text.length > 100 || item.classList.contains('admin-settings-menu')) {
                console.log(`Skipping container item: ${text.substring(0, 50)}...`);
                return;
            }

            const itemInfo = {
                selector: selector,
                index: index,
                text: text,
                dataSection: item.getAttribute('data-section'),
                hasClickHandler: item.onclick !== null || item.hasAttribute('data-nav-fixed'),
                isClickable: window.getComputedStyle(item).cursor === 'pointer',
                pointerEvents: window.getComputedStyle(item).pointerEvents,
                classes: Array.from(item.classList)
            };

            results.navItems.push(itemInfo);

            // Check for issues (skip logout and admin settings container)
            if (!itemInfo.dataSection &&
                !itemInfo.text.includes('تسجيل الخروج') &&
                !itemInfo.text.includes('إعدادات الإدارة') &&
                !item.classList.contains('admin-settings-menu')) {
                results.warnings.push(`Navigation item "${itemInfo.text}" has no data-section attribute`);
            }

            if (!itemInfo.isClickable && itemInfo.dataSection) {
                results.errors.push(`Navigation item "${itemInfo.text}" is not clickable`);
            }

            if (itemInfo.pointerEvents === 'none' && itemInfo.dataSection) {
                results.errors.push(`Navigation item "${itemInfo.text}" has pointer-events: none`);
            }
        });
    });

    // Check sections
    const sectionSelectors = [
        '.content-section',
        '.admin-section',
        '[id$="Content"]',
        '[id$="Section"]'
    ];

    sectionSelectors.forEach(selector => {
        const sections = document.querySelectorAll(selector);
        sections.forEach(section => {
            const sectionInfo = {
                selector: selector,
                id: section.id,
                display: window.getComputedStyle(section).display,
                classes: Array.from(section.classList),
                hasContent: section.innerHTML.trim().length > 0
            };

            results.sections.push(sectionInfo);
        });
    });

    // Check for common issues
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
        results.errors.push('Main content container (.main-content) not found');
    }

    // Check for JavaScript errors
    if (typeof loadDashboard !== 'function') {
        results.warnings.push('loadDashboard function not found');
    }

    if (typeof loadProducts !== 'function' && typeof window.loadProducts !== 'function') {
        results.warnings.push('loadProducts function not found');
    }

    // Display results
    console.log('📊 Navigation Diagnostics Results:', results);

    // Create visual report
    createDiagnosticsReport(results);

    return results;
}

function createDiagnosticsReport(results) {
    // Remove existing report
    const existingReport = document.getElementById('navigationDiagnosticsReport');
    if (existingReport) {
        existingReport.remove();
    }

    // Create report container
    const report = document.createElement('div');
    report.id = 'navigationDiagnosticsReport';
    report.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        background: white;
        border: 2px solid #667eea;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        direction: ltr;
        text-align: left;
    `;

    // Create report content
    let html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #667eea;">🔍 Navigation Diagnostics</h3>
            <button onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">✕</button>
        </div>

        <div style="margin-bottom: 15px;">
            <strong>Timestamp:</strong> ${new Date(results.timestamp).toLocaleString()}
        </div>
    `;

    // Errors section
    if (results.errors.length > 0) {
        html += `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #dc3545; margin: 0 0 10px 0;">❌ Errors (${results.errors.length})</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    ${results.errors.map(error => `<li style="color: #dc3545;">${error}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Warnings section
    if (results.warnings.length > 0) {
        html += `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #f59e0b; margin: 0 0 10px 0;">⚠️ Warnings (${results.warnings.length})</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    ${results.warnings.map(warning => `<li style="color: #f59e0b;">${warning}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Navigation items summary
    const clickableItems = results.navItems.filter(item => item.isClickable).length;
    const totalItems = results.navItems.length;

    html += `
        <div style="margin-bottom: 15px;">
            <h4 style="color: #10b981; margin: 0 0 10px 0;">📋 Navigation Items</h4>
            <p style="margin: 0;">Total: ${totalItems} | Clickable: ${clickableItems} | Fixed: ${results.navItems.filter(item => item.hasClickHandler).length}</p>
        </div>
    `;

    // Sections summary
    const visibleSections = results.sections.filter(section => section.display !== 'none').length;
    const totalSections = results.sections.length;

    html += `
        <div style="margin-bottom: 15px;">
            <h4 style="color: #3b82f6; margin: 0 0 10px 0;">📄 Sections</h4>
            <p style="margin: 0;">Total: ${totalSections} | Visible: ${visibleSections}</p>
        </div>
    `;

    // Action buttons
    html += `
        <div style="display: flex; gap: 10px; margin-top: 15px;">
            <button onclick="runNavigationDiagnostics()" style="background: #667eea; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔄 Refresh
            </button>
            <button onclick="finalNavigationFix.init()" style="background: #10b981; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔧 Fix Now
            </button>
        </div>
    `;

    report.innerHTML = html;
    document.body.appendChild(report);
}

// Auto-run diagnostics
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        runNavigationDiagnostics();
    }, 3000);
});

// Make function globally available
window.runNavigationDiagnostics = runNavigationDiagnostics;

console.log('✅ Navigation Diagnostics loaded');
