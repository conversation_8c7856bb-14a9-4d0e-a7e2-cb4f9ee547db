<?php
echo "=== Debug Connection Test ===\n";

try {
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';
    
    echo "Connecting to: $username@$host:$port/$dbname\n";
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    echo "DSN: $dsn\n";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✓ Connected successfully!\n";
    
    // Simple test query
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "✓ Test query result: " . $result['test'] . "\n";
    
    // Database info
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "✓ Current database: " . $result['db_name'] . "\n";
    
    echo "SUCCESS: Database connection working!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
