/**
 * Final Navigation Fix
 * Comprehensive solution for all navigation issues
 */

console.log('🔧 Final Navigation Fix loading...');

// Configuration
const NAVIGATION_CONFIG = {
    selectors: {
        navItems: [
            '.admin-nav ul li',
            '.sidebar ul li', 
            'nav ul li',
            '[data-section]'
        ],
        sections: [
            '.content-section',
            '.admin-section',
            '[id$="Content"]',
            '[id$="Section"]'
        ]
    },
    sectionMap: {
        'dashboard': { title: 'لوحة المعلومات', icon: 'tachometer-alt' },
        'books': { title: 'إدارة المنتجات', icon: 'box' },
        'orders': { title: 'الطلبات', icon: 'shopping-cart' },
        'landingPages': { title: 'صفحات الهبوط', icon: 'file-alt' },
        'reports': { title: 'التقارير والإحصائيات', icon: 'chart-bar' },
        'settings': { title: 'الإعدادات', icon: 'cog' },
        'categories': { title: 'إدارة الفئات', icon: 'tags' },
        'usersManagement': { title: 'إدارة المستخدمين', icon: 'users' },
        'rolesManagement': { title: 'إدارة الأدوار', icon: 'user-shield' },
        'securitySettings': { title: 'إعدادات الأمان', icon: 'shield-alt' },
        'subscriptionsManagement': { title: 'إدارة الاشتراكات', icon: 'credit-card' }
    }
};

// Main navigation fix function
function finalNavigationFix() {
    console.log('🔧 Applying final navigation fix...');
    
    let fixedCount = 0;
    
    // Process all navigation selectors
    NAVIGATION_CONFIG.selectors.navItems.forEach(selector => {
        const items = document.querySelectorAll(selector);
        console.log(`Processing ${items.length} items for selector: ${selector}`);
        
        items.forEach((item, index) => {
            if (setupNavigationItem(item)) {
                fixedCount++;
            }
        });
    });
    
    console.log(`✅ Fixed ${fixedCount} navigation items`);
    
    // Ensure dashboard is shown by default
    setTimeout(() => {
        const activeItem = document.querySelector('.admin-nav ul li.active');
        if (!activeItem) {
            showSection('dashboard');
        }
    }, 100);
}

// Setup individual navigation item
function setupNavigationItem(item) {
    try {
        // Skip if already processed
        if (item.hasAttribute('data-nav-fixed')) {
            return false;
        }
        
        // Mark as processed
        item.setAttribute('data-nav-fixed', 'true');
        
        // Make clickable
        item.style.cursor = 'pointer';
        item.style.pointerEvents = 'auto';
        item.style.userSelect = 'none';
        
        // Remove existing listeners by cloning
        const newItem = item.cloneNode(true);
        item.parentNode.replaceChild(newItem, item);
        
        // Add click handler
        newItem.addEventListener('click', handleNavigationClick);
        
        // Prevent child elements from interfering
        const children = newItem.querySelectorAll('*');
        children.forEach(child => {
            child.style.pointerEvents = 'none';
        });
        
        console.log(`✅ Fixed navigation item: ${newItem.textContent.trim()}`);
        return true;
        
    } catch (error) {
        console.error('❌ Error setting up navigation item:', error);
        return false;
    }
}

// Handle navigation click
function handleNavigationClick(e) {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🖱️ Navigation clicked:', this.textContent.trim());
    
    // Handle logout
    if (this.textContent.includes('تسجيل الخروج') || this.classList.contains('logout')) {
        if (typeof logout === 'function') {
            logout();
        }
        return;
    }
    
    // Get section ID
    let sectionId = this.getAttribute('data-section');
    
    // Fallback: infer from text content
    if (!sectionId) {
        sectionId = inferSectionFromText(this.textContent.trim());
    }
    
    if (sectionId) {
        showSection(sectionId);
        setActiveNavItem(this);
    } else {
        console.warn('⚠️ Could not determine section for:', this.textContent.trim());
    }
}

// Infer section ID from text content
function inferSectionFromText(text) {
    const textMap = {
        'الرئيسية': 'dashboard',
        'إدارة المنتجات': 'books',
        'الطلبات': 'orders',
        'صفحات هبوط': 'landingPages',
        'صفحات الهبوط': 'landingPages',
        'التقارير': 'reports',
        'الإحصائيات': 'reports',
        'التقارير والإحصائيات': 'reports',
        'الإعدادات': 'settings',
        'الفئات': 'categories',
        'المستخدمين': 'usersManagement',
        'الأدوار': 'rolesManagement',
        'الأمان': 'securitySettings',
        'الاشتراكات': 'subscriptionsManagement'
    };
    
    for (const [key, value] of Object.entries(textMap)) {
        if (text.includes(key)) {
            return value;
        }
    }
    
    return null;
}

// Show section
function showSection(sectionId) {
    console.log(`🎯 Showing section: ${sectionId}`);
    
    // Hide all sections
    NAVIGATION_CONFIG.selectors.sections.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            el.style.display = 'none';
            el.classList.remove('active');
        });
    });
    
    // Show target section
    let targetSection = document.getElementById(sectionId);
    
    if (!targetSection) {
        console.log(`Creating section: ${sectionId}`);
        targetSection = createSection(sectionId);
    }
    
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active');
        console.log(`✅ Section ${sectionId} shown`);
        
        // Load content
        loadSectionContent(sectionId);
    }
}

// Create section if it doesn't exist
function createSection(sectionId) {
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
        console.error('❌ Main content container not found');
        return null;
    }
    
    const sectionInfo = NAVIGATION_CONFIG.sectionMap[sectionId] || { 
        title: sectionId, 
        icon: 'file' 
    };
    
    const section = document.createElement('div');
    section.id = sectionId;
    section.className = 'content-section';
    section.style.display = 'block';
    section.style.padding = '20px';
    
    section.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h2 style="color: #2d3748; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-${sectionInfo.icon}"></i>
                ${sectionInfo.title}
            </h2>
            <div id="${sectionId}Content">
                <div style="text-align: center; padding: 40px; color: #4a5568;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <p>جاري تحميل المحتوى...</p>
                </div>
            </div>
        </div>
    `;
    
    mainContent.appendChild(section);
    console.log(`✅ Created section: ${sectionId}`);
    
    return section;
}

// Set active navigation item
function setActiveNavItem(clickedItem) {
    // Remove active class from all items
    document.querySelectorAll('.admin-nav ul li').forEach(item => {
        item.classList.remove('active');
    });
    
    // Add active class to clicked item
    clickedItem.classList.add('active');
}

// Load section content
function loadSectionContent(sectionId) {
    console.log(`📄 Loading content for: ${sectionId}`);
    
    try {
        switch (sectionId) {
            case 'dashboard':
                if (typeof loadDashboard === 'function') {
                    loadDashboard();
                } else {
                    loadDefaultDashboard();
                }
                break;
                
            case 'books':
                if (typeof loadProducts === 'function') {
                    loadProducts();
                } else if (typeof window.loadProducts === 'function') {
                    window.loadProducts();
                } else {
                    loadDefaultProducts();
                }
                break;
                
            case 'orders':
                if (typeof loadOrders === 'function') {
                    loadOrders();
                } else {
                    loadDefaultOrders();
                }
                break;
                
            case 'landingPages':
                if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                    landingPagesManager.loadLandingPages();
                } else {
                    loadDefaultLandingPages();
                }
                break;
                
            case 'reports':
                if (typeof loadReportsAndStatistics === 'function') {
                    loadReportsAndStatistics();
                } else {
                    loadDefaultReports();
                }
                break;
                
            default:
                loadDefaultContent(sectionId);
        }
    } catch (error) {
        console.error(`❌ Error loading content for ${sectionId}:`, error);
        showErrorContent(sectionId, error);
    }
}

// Default content loaders
function loadDefaultDashboard() {
    const container = document.getElementById('dashboardContent') || document.getElementById('dashboard');
    if (container) {
        container.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-box" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>المنتجات</h3>
                    <p style="font-size: 1.5rem; margin: 0;">--</p>
                </div>
                <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>الطلبات</h3>
                    <p style="font-size: 1.5rem; margin: 0;">--</p>
                </div>
                <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>المبيعات</h3>
                    <p style="font-size: 1.5rem; margin: 0;">--</p>
                </div>
            </div>
        `;
    }
}

function loadDefaultProducts() {
    const container = document.getElementById('booksContent') || document.getElementById('books');
    if (container) {
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>قائمة المنتجات</h3>
                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                <p>جاري تحميل المنتجات...</p>
            </div>
        `;
    }
}

function loadDefaultOrders() {
    const container = document.getElementById('ordersContent') || document.getElementById('orders');
    if (container) {
        container.innerHTML = `
            <h3 style="margin-bottom: 20px;">قائمة الطلبات</h3>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                <p>جاري تحميل الطلبات...</p>
            </div>
        `;
    }
}

function loadDefaultLandingPages() {
    const container = document.getElementById('landingPagesContent') || document.getElementById('landingPages');
    if (container) {
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>صفحات الهبوط</h3>
                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة صفحة
                </button>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                <p>جاري تحميل صفحات الهبوط...</p>
            </div>
        `;
    }
}

function loadDefaultReports() {
    const container = document.getElementById('reportsContent') || document.getElementById('reports');
    if (container) {
        container.innerHTML = `
            <h3 style="margin-bottom: 20px;">التقارير والإحصائيات</h3>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center;">
                <p>جاري تحميل التقارير...</p>
            </div>
        `;
    }
}

function loadDefaultContent(sectionId) {
    const container = document.getElementById(`${sectionId}Content`) || document.getElementById(sectionId);
    if (container) {
        const sectionInfo = NAVIGATION_CONFIG.sectionMap[sectionId] || { title: sectionId };
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #4a5568;">
                <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3>${sectionInfo.title}</h3>
                <p>هذا القسم قيد التطوير</p>
            </div>
        `;
    }
}

function showErrorContent(sectionId, error) {
    const container = document.getElementById(`${sectionId}Content`) || document.getElementById(sectionId);
    if (container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل المحتوى</h4>
                <p>${error.message}</p>
                <button onclick="finalNavigationFix.showSection('${sectionId}')" style="margin-top: 15px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Final Navigation Fix - DOM Ready');
    
    // Apply fix with delays to ensure all scripts are loaded
    setTimeout(finalNavigationFix, 500);
    setTimeout(finalNavigationFix, 2000);
    setTimeout(finalNavigationFix, 5000);
});

// Initialize when window loads
window.addEventListener('load', function() {
    console.log('🔧 Final Navigation Fix - Window Loaded');
    setTimeout(finalNavigationFix, 1000);
});

// Export functions globally
const finalNavigationFixAPI = {
    init: finalNavigationFix,
    showSection: showSection,
    loadSectionContent: loadSectionContent
};

window.finalNavigationFix = finalNavigationFixAPI;

console.log('✅ Final Navigation Fix loaded successfully');
