<?php
session_start();
require_once '../php/config.php';

echo "<h1>🔐 Test du système de connexion</h1>";

try {
    // Test database connection
    echo "<h2>1. Test de connexion à la base de données</h2>";
    $pdo = getPDOConnection();
    echo "✅ Connexion à la base de données réussie<br>";
    
    // Check if admins table exists
    echo "<h2>2. Vérification de la table admins</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table 'admins' existe<br>";
        
        // Count admin users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins");
        $count = $stmt->fetch()['count'];
        echo "📊 Nombre d'administrateurs : $count<br>";
        
        if ($count > 0) {
            // Show admin users
            echo "<h3>Utilisateurs administrateurs :</h3>";
            $stmt = $pdo->query("SELECT nom_utilisateur, email, role, status FROM admins");
            $admins = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Nom d'utilisateur</th><th>Email</th><th>Rôle</th><th>Statut</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>{$admin['nom_utilisateur']}</td>";
                echo "<td>{$admin['email']}</td>";
                echo "<td>{$admin['role']}</td>";
                echo "<td>{$admin['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "⚠️ Aucun utilisateur administrateur trouvé<br>";
            echo "<a href='setup-demo-users.php'>Créer des utilisateurs de démonstration</a><br>";
        }
    } else {
        echo "❌ Table 'admins' n'existe pas<br>";
        echo "<a href='setup-demo-users.php'>Créer la table et les utilisateurs de démonstration</a><br>";
    }
    
    // Test authentication functions
    echo "<h2>3. Test des fonctions d'authentification</h2>";
    
    if (function_exists('isAdminLoggedIn')) {
        echo "✅ Fonction isAdminLoggedIn() existe<br>";
        $isLoggedIn = isAdminLoggedIn();
        echo "📊 Statut de connexion actuel : " . ($isLoggedIn ? "Connecté" : "Non connecté") . "<br>";
    } else {
        echo "❌ Fonction isAdminLoggedIn() n'existe pas<br>";
    }
    
    // Test login API endpoint
    echo "<h2>4. Test de l'endpoint de connexion</h2>";
    $loginUrl = '../php/admin.php?action=check';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json'
        ]
    ]);
    
    $response = @file_get_contents($loginUrl, false, $context);
    if ($response !== false) {
        echo "✅ Endpoint de vérification accessible<br>";
        $data = json_decode($response, true);
        if ($data !== null) {
            echo "✅ Réponse JSON valide : " . json_encode($data) . "<br>";
        } else {
            echo "⚠️ Réponse non-JSON : " . htmlspecialchars($response) . "<br>";
        }
    } else {
        echo "❌ Impossible d'accéder à l'endpoint de vérification<br>";
    }
    
    echo "<h2>5. Instructions de test</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Pour tester la connexion :</strong><br>";
    echo "1. <a href='setup-demo-users.php' target='_blank'>Créer les utilisateurs de démonstration</a><br>";
    echo "2. <a href='login.html' target='_blank'>Aller à la page de connexion</a><br>";
    echo "3. Utiliser un des comptes suivants :<br>";
    echo "&nbsp;&nbsp;• <strong>admin</strong> / admin123<br>";
    echo "&nbsp;&nbsp;• <strong>mossaab</strong> / mossaab2024<br>";
    echo "&nbsp;&nbsp;• <strong>manager</strong> / manager123<br>";
    echo "&nbsp;&nbsp;• <strong>demo</strong> / demo123<br>";
    echo "4. <a href='index.html' target='_blank'>Accéder au tableau de bord</a><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Erreur : " . $e->getMessage() . "<br>";
    echo "<br><strong>Solutions possibles :</strong><br>";
    echo "1. Vérifier la configuration de la base de données dans .env<br>";
    echo "2. S'assurer que MySQL est démarré sur le port 3307<br>";
    echo "3. Créer la base de données 'mossab-landing-page' si elle n'existe pas<br>";
}

echo "<br><h2>🔗 Liens utiles</h2>";
echo "<ul>";
echo "<li><a href='setup-demo-users.php'>Configurer les utilisateurs de démonstration</a></li>";
echo "<li><a href='login.html'>Page de connexion</a></li>";
echo "<li><a href='index.html'>Tableau de bord admin</a></li>";
echo "<li><a href='../index.html'>Site principal</a></li>";
echo "</ul>";
?>
