<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل Firebase - متجر مصعب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .steps {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .steps h3 {
            color: #004085;
            margin-bottom: 15px;
        }
        
        .steps ol {
            padding-right: 20px;
        }
        
        .steps li {
            margin-bottom: 10px;
            color: #004085;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-family: inherit;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .accounts-table th,
        .accounts-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        
        .accounts-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .accounts-table tr:nth-child(even) {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 دليل Firebase Authentication</h1>
            <p>نظام المصادقة المتقدم لمتجر مصعب</p>
        </div>
        
        <div class="content">
            <!-- Overview -->
            <div class="section">
                <h2>📋 نظرة عامة</h2>
                <p>تم تطوير نظام مصادقة متقدم باستخدام Firebase Authentication يدعم تسجيل الدخول بالبريد الإلكتروني وكلمة المرور، بالإضافة إلى تسجيل الدخول عبر Google.</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔐</div>
                        <div class="feature-title">مصادقة آمنة</div>
                        <div class="feature-description">
                            نظام مصادقة متقدم مع تشفير البيانات وحماية من الهجمات
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📧</div>
                        <div class="feature-title">تسجيل بالبريد الإلكتروني</div>
                        <div class="feature-description">
                            إمكانية إنشاء حساب وتسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🌐</div>
                        <div class="feature-title">تسجيل دخول Google</div>
                        <div class="feature-description">
                            تسجيل دخول سريع وآمن باستخدام حساب Google
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <div class="feature-title">إدارة المستخدمين</div>
                        <div class="feature-description">
                            لوحة تحكم متقدمة لإدارة المستخدمين والأدوار
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <div class="feature-title">نظام الأدوار</div>
                        <div class="feature-description">
                            تحكم دقيق في الصلاحيات حسب دور كل مستخدم
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <div class="feature-title">تتبع النشاط</div>
                        <div class="feature-description">
                            مراقبة نشاط المستخدمين وإحصائيات مفصلة
                        </div>
                    </div>
                </div>
            </div>

            <!-- Getting Started -->
            <div class="section">
                <h2>🚀 البدء السريع</h2>
                
                <div class="steps">
                    <h3>خطوات الإعداد الأولي</h3>
                    <ol>
                        <li><strong>إنشاء حسابات المديرين:</strong> ابدأ بإنشاء حسابات المديرين الأساسية</li>
                        <li><strong>تسجيل الدخول:</strong> استخدم أحد الحسابات المنشأة لتسجيل الدخول</li>
                        <li><strong>إدارة المستخدمين:</strong> استكشف لوحة إدارة المستخدمين</li>
                        <li><strong>تخصيص الأدوار:</strong> قم بتعيين الأدوار المناسبة للمستخدمين</li>
                    </ol>
                </div>

                <div class="buttons">
                    <a href="setup-firebase-admins.html" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> إنشاء حسابات المديرين
                    </a>
                    <a href="firebase-login.html" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                    <a href="firebase-users.html" class="btn btn-info">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </a>
                </div>
            </div>

            <!-- Admin Accounts -->
            <div class="section">
                <h2>👑 حسابات المديرين</h2>
                
                <div class="info-box">
                    <strong>📝 ملاحظة:</strong> يجب إنشاء هذه الحسابات أولاً باستخدام صفحة إعداد المديرين.
                </div>

                <table class="accounts-table">
                    <thead>
                        <tr>
                            <th>الدور</th>
                            <th>البريد الإلكتروني</th>
                            <th>كلمة المرور</th>
                            <th>الصلاحيات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>مدير رئيسي</td>
                            <td><EMAIL></td>
                            <td>Admin123!@#</td>
                            <td>صلاحيات كاملة</td>
                        </tr>
                        <tr>
                            <td>مالك المتجر</td>
                            <td><EMAIL></td>
                            <td>Mossaab2024!</td>
                            <td>إدارة المتجر والمنتجات</td>
                        </tr>
                        <tr>
                            <td>مدير</td>
                            <td><EMAIL></td>
                            <td>Manager123!</td>
                            <td>إدارة محدودة</td>
                        </tr>
                        <tr>
                            <td>تجريبي</td>
                            <td><EMAIL></td>
                            <td>Demo123!</td>
                            <td>عرض فقط</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Features -->
            <div class="section">
                <h2>✨ المميزات المتقدمة</h2>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-title">🔄 المزامنة التلقائية</div>
                        <div class="feature-description">
                            تحديث البيانات في الوقت الفعلي عبر جميع الأجهزة
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">🌍 دعم متعدد اللغات</div>
                        <div class="feature-description">
                            واجهة باللغة العربية مع دعم RTL كامل
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">📱 متجاوب</div>
                        <div class="feature-description">
                            يعمل بشكل مثالي على جميع الأجهزة والشاشات
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">🔒 أمان متقدم</div>
                        <div class="feature-description">
                            حماية متعددة الطبقات وتشفير البيانات
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Info -->
            <div class="section">
                <h2>⚙️ المعلومات التقنية</h2>
                
                <div class="success-box">
                    <strong>🔧 التقنيات المستخدمة:</strong>
                    <ul style="margin-top: 10px; padding-right: 20px;">
                        <li>Firebase Authentication v10.7.1</li>
                        <li>Firebase Firestore للبيانات</li>
                        <li>Firebase Analytics للإحصائيات</li>
                        <li>JavaScript ES6 Modules</li>
                        <li>CSS Grid & Flexbox</li>
                        <li>Font Awesome Icons</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <strong>⚠️ متطلبات النظام:</strong>
                    <ul style="margin-top: 10px; padding-right: 20px;">
                        <li>متصفح حديث يدعم ES6 Modules</li>
                        <li>اتصال بالإنترنت لـ Firebase</li>
                        <li>JavaScript مفعل</li>
                        <li>Cookies مفعلة للجلسات</li>
                    </ul>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="section">
                <h2>⚡ إجراءات سريعة</h2>
                
                <div class="buttons">
                    <a href="setup-firebase-admins.html" class="btn btn-success">
                        <i class="fas fa-cog"></i> إعداد المديرين
                    </a>
                    <a href="firebase-login.html" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                    <a href="firebase-users.html" class="btn btn-info">
                        <i class="fas fa-users-cog"></i> إدارة المستخدمين
                    </a>
                    <a href="index.html" class="btn btn-warning">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </a>
                </div>
            </div>

            <!-- Support -->
            <div class="section">
                <h2>🆘 الدعم والمساعدة</h2>
                
                <div class="info-box">
                    <strong>💡 نصائح للاستخدام:</strong>
                    <ul style="margin-top: 10px; padding-right: 20px;">
                        <li>ابدأ بإنشاء حسابات المديرين من صفحة الإعداد</li>
                        <li>استخدم تسجيل الدخول بـ Google للوصول السريع</li>
                        <li>راجع صفحة إدارة المستخدمين لمراقبة النشاط</li>
                        <li>تأكد من تعيين الأدوار المناسبة لكل مستخدم</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
