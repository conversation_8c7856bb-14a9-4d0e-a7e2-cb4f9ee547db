<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 تشخيص الخادم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .diagnostic-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1 class="text-center mb-4">🔧 تشخيص مشاكل الخادم</h1>
        
        <div class="text-center mb-4">
            <button id="run-diagnostics" class="btn btn-primary btn-lg">🚀 تشغيل التشخيص</button>
        </div>
        
        <div id="results"></div>
        
        <div class="mt-4">
            <h4>الحلول المقترحة:</h4>
            <div class="alert alert-info">
                <h6>إذا كانت ملفات PHP تتحمل بدلاً من التنفيذ:</h6>
                <ol>
                    <li><strong>تأكد من تشغيل خادم PHP:</strong>
                        <div class="code-block">php -S localhost:8000</div>
                    </li>
                    <li><strong>أو استخدم XAMPP/WAMP/MAMP</strong></li>
                    <li><strong>تحقق من إعدادات Apache/Nginx</strong></li>
                </ol>
            </div>
            
            <div class="alert alert-warning">
                <h6>إذا كانت قاعدة البيانات لا تعمل:</h6>
                <ol>
                    <li><strong>تأكد من تشغيل MySQL/MariaDB</strong></li>
                    <li><strong>تحقق من المنفذ (3306 أو 3307)</strong></li>
                    <li><strong>تأكد من وجود قاعدة البيانات 'poultraydz'</strong></li>
                    <li><strong>تحقق من بيانات المستخدم (root/root أو root/فارغ)</strong></li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        async function runDiagnostics() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner-border" role="status"></div><p>جاري تشغيل التشخيص...</p></div>';
            
            const tests = [
                {
                    name: 'اختبار معالجة PHP',
                    description: 'التحقق من أن الخادم يعالج ملفات PHP',
                    test: testPHPProcessing
                },
                {
                    name: 'اختبار الاتصال بقاعدة البيانات',
                    description: 'اختبار إعدادات قاعدة البيانات المختلفة',
                    test: testDatabaseConnection
                },
                {
                    name: 'اختبار CORS',
                    description: 'التحقق من إعدادات CORS',
                    test: testCORS
                }
            ];
            
            let html = '<h4>نتائج التشخيص:</h4>';
            
            for (const test of tests) {
                try {
                    const result = await test.test();
                    const cssClass = result.success ? 'test-success' : 'test-error';
                    const icon = result.success ? '✅' : '❌';
                    
                    html += `
                        <div class="test-result ${cssClass}">
                            <h6>${icon} ${test.name}</h6>
                            <p><strong>الوصف:</strong> ${test.description}</p>
                            <p><strong>النتيجة:</strong> ${result.message}</p>
                            ${result.details ? `<div class="code-block">${result.details}</div>` : ''}
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="test-result test-error">
                            <h6>❌ ${test.name}</h6>
                            <p><strong>خطأ:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            resultsDiv.innerHTML = html;
        }
        
        async function testPHPProcessing() {
            try {
                // Try to fetch a simple PHP info endpoint
                const response = await fetch('../php/api/test-db-configs.php');
                const contentType = response.headers.get('content-type');
                
                if (response.ok && contentType && contentType.includes('application/json')) {
                    return {
                        success: true,
                        message: 'الخادم يعالج ملفات PHP بشكل صحيح',
                        details: `Content-Type: ${contentType}`
                    };
                } else {
                    const text = await response.text();
                    if (text.includes('<?php')) {
                        return {
                            success: false,
                            message: 'الخادم لا يعالج ملفات PHP - يتم إرجاع الكود الخام',
                            details: 'الخادم يحتاج إلى إعداد PHP'
                        };
                    } else {
                        return {
                            success: false,
                            message: 'استجابة غير متوقعة من الخادم',
                            details: text.substring(0, 200)
                        };
                    }
                }
            } catch (error) {
                return {
                    success: false,
                    message: 'فشل في الاتصال بالخادم: ' + error.message
                };
            }
        }
        
        async function testDatabaseConnection() {
            try {
                const response = await fetch('../php/api/test-db-configs.php');
                
                if (!response.ok) {
                    return {
                        success: false,
                        message: `خطأ HTTP: ${response.status} ${response.statusText}`
                    };
                }
                
                const data = await response.json();
                
                if (data.successful_connections > 0) {
                    return {
                        success: true,
                        message: `تم العثور على ${data.successful_connections} إعداد يعمل من أصل ${data.total_configs_tested}`,
                        details: data.working_config ? 
                            `الإعداد الموصى به: ${data.working_config.host}:${data.working_config.port || 'default'} - ${data.working_config.username}` : 
                            'لا يوجد إعداد موصى به'
                    };
                } else {
                    return {
                        success: false,
                        message: 'فشل في جميع إعدادات قاعدة البيانات',
                        details: 'تحقق من تشغيل MySQL وصحة بيانات الاعتماد'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    message: 'فشل في اختبار قاعدة البيانات: ' + error.message
                };
            }
        }
        
        async function testCORS() {
            try {
                const response = await fetch('../php/api/test-connection.php', {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                if (response.ok && corsHeaders['Access-Control-Allow-Origin']) {
                    return {
                        success: true,
                        message: 'إعدادات CORS تعمل بشكل صحيح',
                        details: JSON.stringify(corsHeaders, null, 2)
                    };
                } else {
                    return {
                        success: false,
                        message: 'مشكلة في إعدادات CORS',
                        details: `Status: ${response.status}`
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    message: 'فشل في اختبار CORS: ' + error.message
                };
            }
        }
        
        document.getElementById('run-diagnostics').addEventListener('click', runDiagnostics);
        
        // Run diagnostics automatically on load
        window.addEventListener('load', () => {
            setTimeout(runDiagnostics, 1000);
        });
    </script>
</body>
</html>
