<?php
/**
 * Quick Database Setup Script
 * Creates database and roles table if they don't exist
 */

echo "=== Database Setup Script ===\n";

try {
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';
    
    echo "Connecting to MySQL server...\n";
    
    // Connect without database first
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✓ Connected to MySQL server\n";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() == 0) {
        echo "Creating database '$dbname'...\n";
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✓ Database created\n";
    } else {
        echo "✓ Database '$dbname' already exists\n";
    }
    
    // Connect to the database
    $pdo->exec("USE `$dbname`");
    echo "✓ Connected to database\n";
    
    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() == 0) {
        echo "Creating roles table...\n";
        
        $createRolesSQL = "
            CREATE TABLE roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL DEFAULT '',
                display_name_ar VARCHAR(100) NOT NULL DEFAULT '',
                description TEXT DEFAULT NULL,
                permissions JSON DEFAULT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createRolesSQL);
        echo "✓ Roles table created\n";
        
        // Insert default roles
        $defaultRoles = [
            ['admin', 'Administrator', 'مدير النظام', 'Full system access'],
            ['seller', 'Seller', 'بائع', 'Can manage own products and orders'],
            ['user', 'User', 'مستخدم', 'Basic user access'],
            ['moderator', 'Moderator', 'مشرف', 'Can moderate content'],
            ['editor', 'Editor', 'محرر', 'Can edit content']
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO roles (name, display_name, display_name_ar, description) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($defaultRoles as $role) {
            $insertStmt->execute($role);
        }
        
        echo "✓ Inserted " . count($defaultRoles) . " default roles\n";
    } else {
        echo "✓ Roles table already exists\n";
        
        // Check role count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $count = $stmt->fetch()['count'];
        echo "✓ Found $count roles in table\n";
    }
    
    // Final verification
    $stmt = $pdo->query("SELECT id, name, display_name_ar FROM roles ORDER BY id LIMIT 3");
    $roles = $stmt->fetchAll();
    
    echo "\n=== Setup Complete ===\n";
    echo "Database: $dbname\n";
    echo "Roles table: ✓ Ready\n";
    echo "Sample roles:\n";
    foreach ($roles as $role) {
        echo "  - {$role['name']} ({$role['display_name_ar']})\n";
    }
    
    echo "\n🎉 Database setup successful!\n";
    echo "You can now test the web interface.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nTroubleshooting:\n";
    echo "1. Make sure MySQL/MariaDB is running on port 3307\n";
    echo "2. Check that user 'root' has no password\n";
    echo "3. Verify the database server is accessible\n";
}
?>
