<?php
/**
 * Script pour diagnostiquer et corriger les problèmes de serveur
 */

echo "<h1>🔧 Diagnostic et correction des problèmes de serveur</h1>";

// Test 1: Vérifier si PHP fonctionne
echo "<h2>1. Test PHP</h2>";
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
echo "✅ PHP fonctionne correctement<br>";
echo "Version PHP: " . PHP_VERSION . "<br>";
echo "Serveur: " . $_SERVER['SERVER_SOFTWARE'] ?? 'Inconnu' . "<br>";
echo "</div>";

// Test 2: Vérifier la base de données
echo "<h2>2. Test de base de données</h2>";
try {
    require_once '../php/config.php';
    $pdo = getPDOConnection();
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ Connexion à la base de données réussie<br>";
    
    // Test table admins
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table 'admins' existe<br>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins");
        $count = $stmt->fetch()['count'];
        echo "📊 Nombre d'administrateurs: $count<br>";
    } else {
        echo "⚠️ Table 'admins' n'existe pas<br>";
        echo "<a href='setup-demo-users.php'>Créer les utilisateurs de démonstration</a><br>";
    }
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ Erreur de base de données: " . $e->getMessage() . "<br>";
    echo "</div>";
}

// Test 3: Vérifier les API
echo "<h2>3. Test des API</h2>";
$apiTests = [
    'admin.php?action=check' => 'Vérification d\'authentification',
    'api/dashboard-stats.php' => 'Statistiques du tableau de bord',
    'api/get-ai-settings.php' => 'Paramètres IA',
    'api/get-security-settings.php' => 'Paramètres de sécurité'
];

foreach ($apiTests as $endpoint => $description) {
    $url = "../php/$endpoint";
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<strong>$description</strong><br>";
    echo "URL: $url<br>";
    
    if (file_exists($url)) {
        echo "<span style='color: #28a745;'>✅ Fichier existe</span><br>";
        
        // Test simple d'inclusion
        ob_start();
        try {
            $output = file_get_contents("http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/../php/$endpoint");
            if ($output && !empty(trim($output))) {
                if (strpos($output, '<?php') === 0) {
                    echo "<span style='color: #dc3545;'>❌ Retourne du code PHP (serveur ne traite pas PHP)</span><br>";
                } else {
                    echo "<span style='color: #28a745;'>✅ Retourne une réponse</span><br>";
                }
            } else {
                echo "<span style='color: #ffc107;'>⚠️ Réponse vide</span><br>";
            }
        } catch (Exception $e) {
            echo "<span style='color: #dc3545;'>❌ Erreur: " . $e->getMessage() . "</span><br>";
        }
        ob_end_clean();
    } else {
        echo "<span style='color: #dc3545;'>❌ Fichier n'existe pas</span><br>";
    }
    echo "</div>";
}

// Solutions recommandées
echo "<h2>4. Solutions recommandées</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff;'>";
echo "<h3>🚀 Solution principale : Utiliser le serveur PHP intégré</h3>";
echo "<ol>";
echo "<li>Fermez votre serveur actuel (Ctrl+C)</li>";
echo "<li>Double-cliquez sur <strong>start-php-server.bat</strong></li>";
echo "<li>Ou exécutez dans le terminal : <code>php -S localhost:8080 router.php</code></li>";
echo "<li>Accédez à : <a href='http://localhost:8080/admin/'>http://localhost:8080/admin/</a></li>";
echo "</ol>";

echo "<h3>🔧 Solutions alternatives</h3>";
echo "<ul>";
echo "<li><a href='quick-login-test.php'>Test de connexion rapide</a> - Connexion directe sans serveur</li>";
echo "<li><a href='login-simple.html'>Page de connexion simplifiée</a> - Avec fallback local</li>";
echo "<li><a href='setup-demo-users.php'>Configuration des utilisateurs</a> - Créer les comptes</li>";
echo "</ul>";
echo "</div>";

// Instructions détaillées
echo "<h2>5. Instructions détaillées</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
echo "<h3>⚠️ Problème détecté</h3>";
echo "<p>Votre serveur actuel ne traite pas les fichiers PHP correctement. Il retourne le code source PHP au lieu de l'exécuter.</p>";

echo "<h3>✅ Solution</h3>";
echo "<p><strong>Utilisez le serveur PHP intégré :</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>php -S localhost:8080 router.php</pre>";

echo "<h3>🔗 Liens de test après correction</h3>";
echo "<ul>";
echo "<li><a href='http://localhost:8080/admin/setup-demo-users.php' target='_blank'>Configuration des utilisateurs</a></li>";
echo "<li><a href='http://localhost:8080/admin/login-simple.html' target='_blank'>Page de connexion</a></li>";
echo "<li><a href='http://localhost:8080/admin/' target='_blank'>Tableau de bord admin</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. Comptes de test disponibles</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th>Utilisateur</th><th>Mot de passe</th><th>Rôle</th></tr>";
echo "<tr><td>admin</td><td>admin123</td><td>Super Admin</td></tr>";
echo "<tr><td>mossaab</td><td>mossaab2024</td><td>Owner</td></tr>";
echo "<tr><td>manager</td><td>manager123</td><td>Manager</td></tr>";
echo "<tr><td>demo</td><td>demo123</td><td>Demo</td></tr>";
echo "</table>";
?>
