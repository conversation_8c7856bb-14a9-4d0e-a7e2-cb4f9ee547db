<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Firebase Auth</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .auth-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .auth-header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }
        
        .auth-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .auth-content {
            padding: 30px;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            border-radius: 10px;
            background: #f8f9fa;
            padding: 5px;
        }
        
        .auth-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .auth-tab.active {
            background: #667eea;
            color: white;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .auth-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            font-family: inherit;
        }
        
        .auth-button.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .auth-button.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .auth-button.google {
            background: white;
            color: #333;
            border: 2px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .auth-button.google:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #6c757d;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>🔐 تسجيل الدخول</h1>
            <p>متجر مصعب - لوحة التحكم</p>
        </div>
        
        <div class="auth-content">
            <!-- Loading State -->
            <div class="loading" id="loadingState">
                <div class="spinner"></div>
                <p>جاري التحميل...</p>
            </div>
            
            <!-- Auth Tabs -->
            <div class="auth-tabs" id="authTabs">
                <div class="auth-tab active" data-tab="signin">تسجيل الدخول</div>
                <div class="auth-tab" data-tab="signup">إنشاء حساب</div>
            </div>
            
            <!-- Messages -->
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <!-- Sign In Form -->
            <form class="auth-form active" id="signinForm">
                <div class="form-group">
                    <label for="signinEmail">البريد الإلكتروني</label>
                    <input type="email" id="signinEmail" required>
                </div>
                <div class="form-group">
                    <label for="signinPassword">كلمة المرور</label>
                    <input type="password" id="signinPassword" required>
                </div>
                <button type="submit" class="auth-button primary">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>
            
            <!-- Sign Up Form -->
            <form class="auth-form" id="signupForm">
                <div class="form-group">
                    <label for="signupName">الاسم الكامل</label>
                    <input type="text" id="signupName" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">البريد الإلكتروني</label>
                    <input type="email" id="signupEmail" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">كلمة المرور</label>
                    <input type="password" id="signupPassword" required minlength="6">
                </div>
                <button type="submit" class="auth-button primary">
                    <i class="fas fa-user-plus"></i> إنشاء حساب
                </button>
            </form>
            
            <!-- Divider -->
            <div class="divider">
                <span>أو</span>
            </div>
            
            <!-- Google Sign In -->
            <button class="auth-button google" id="googleSignInBtn">
                <i class="fab fa-google"></i>
                تسجيل الدخول بـ Google
            </button>
            
            <!-- Back Link -->
            <div class="back-link">
                <a href="../index.html">العودة إلى الموقع</a>
            </div>
        </div>
    </div>

    <!-- Firebase and App Scripts -->
    <script type="module">
        import './js/firebase-config.js';
        
        // Wait for Firebase to initialize
        setTimeout(() => {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('authTabs').style.display = 'flex';
        }, 1000);
        
        // Tab switching
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active form
                document.querySelectorAll('.auth-form').forEach(f => f.classList.remove('active'));
                document.getElementById(tabName + 'Form').classList.add('active');
                
                // Clear messages
                clearMessages();
            });
        });
        
        // Sign In Form
        document.getElementById('signinForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('signinEmail').value;
            const password = document.getElementById('signinPassword').value;
            
            setLoading(true);
            clearMessages();
            
            const result = await window.firebaseAuth.signInWithEmail(email, password);
            
            if (result.success) {
                showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showError(result.error);
            }
            
            setLoading(false);
        });
        
        // Sign Up Form
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('signupName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            
            setLoading(true);
            clearMessages();
            
            const result = await window.firebaseAuth.signUpWithEmail(email, password, name);
            
            if (result.success) {
                showSuccess('تم إنشاء الحساب بنجاح! جاري التوجيه...');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showError(result.error);
            }
            
            setLoading(false);
        });
        
        // Google Sign In
        document.getElementById('googleSignInBtn').addEventListener('click', async () => {
            setLoading(true);
            clearMessages();
            
            const result = await window.firebaseAuth.signInWithGoogle();
            
            if (result.success) {
                showSuccess('تم تسجيل الدخول بـ Google بنجاح! جاري التوجيه...');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showError(result.error);
            }
            
            setLoading(false);
        });
        
        // Utility functions
        function setLoading(loading) {
            const buttons = document.querySelectorAll('.auth-button');
            buttons.forEach(btn => btn.disabled = loading);
            
            if (loading) {
                buttons.forEach(btn => {
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                });
            } else {
                document.querySelector('#signinForm .auth-button').innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
                document.querySelector('#signupForm .auth-button').innerHTML = '<i class="fas fa-user-plus"></i> إنشاء حساب';
                document.getElementById('googleSignInBtn').innerHTML = '<i class="fab fa-google"></i> تسجيل الدخول بـ Google';
            }
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
        
        function clearMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
        
        // Check if user is already signed in
        window.onFirebaseUserSignedIn = (user, profile) => {
            console.log('User already signed in, redirecting...');
            window.location.href = 'index.html';
        };
    </script>
</body>
</html>
