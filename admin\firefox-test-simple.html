<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦊 Firefox Test - Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .browser-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🦊 Firefox Compatibility Test - Simple</h1>
        
        <div class="browser-info">
            <h5>معلومات المتصفح</h5>
            <div id="browser-details"></div>
        </div>
        
        <div class="text-center mb-4">
            <button id="enable-mock" class="btn btn-warning btn-lg me-2">🔧 تفعيل Mock Server</button>
            <button id="run-tests" class="btn btn-primary btn-lg">🧪 تشغيل الاختبارات</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="alert alert-info mt-4">
            <h6><strong>ملاحظة:</strong></h6>
            <p>هذا الاختبار المبسط يعمل حتى لو كان خادم PHP لا يعمل بشكل صحيح.</p>
            <p>للحصول على النتائج الحقيقية، يجب تشغيل خادم PHP بالأمر: <code>php -S localhost:8000</code></p>
        </div>
    </div>

    <script>
        let mockEnabled = false;
        
        // Mock responses
        const mockData = {
            'test-connection.php': {
                success: true,
                message: 'Database connection successful (Mock)',
                database_info: { name: 'mossab-landing-page', host: 'localhost', port: '3307' },
                roles_table: { exists: true, count: 5 }
            },
            'test-roles.php': {
                success: true,
                message: 'Roles retrieved successfully (Mock)',
                roles: [
                    { id: 1, name: 'admin', display_name_ar: 'مدير النظام' },
                    { id: 2, name: 'seller', display_name_ar: 'بائع' }
                ],
                total: 2
            }
        };
        
        // Override fetch when mock is enabled
        const originalFetch = window.fetch;
        
        function enableMockServer() {
            mockEnabled = true;
            
            window.fetch = function(url, options = {}) {
                if (mockEnabled) {
                    // Check if this URL should be mocked
                    for (const [key, response] of Object.entries(mockData)) {
                        if (url.includes(key)) {
                            return Promise.resolve({
                                ok: true,
                                status: 200,
                                headers: new Headers({ 'content-type': 'application/json' }),
                                json: () => Promise.resolve(response),
                                text: () => Promise.resolve(JSON.stringify(response))
                            });
                        }
                    }
                }
                
                return originalFetch(url, options);
            };
            
            document.getElementById('enable-mock').innerHTML = '✅ Mock Server Active';
            document.getElementById('enable-mock').disabled = true;
        }
        
        function displayBrowserInfo() {
            const userAgent = navigator.userAgent;
            const isFirefox = userAgent.toLowerCase().indexOf("firefox") > -1;
            const browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/);
            
            document.getElementById("browser-details").innerHTML = `
                <p><strong>User Agent:</strong> ${userAgent}</p>
                <p><strong>Is Firefox:</strong> ${isFirefox ? "✅ Yes" : "❌ No"}</p>
                ${browserVersion ? `<p><strong>Firefox Version:</strong> ${browserVersion[1]}</p>` : ""}
                <p><strong>Compatibility Mode:</strong> ${mockEnabled ? "🔧 Mock Active" : "🔄 Standard"}</p>
            `;
        }
        
        async function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>جاري تشغيل الاختبارات...</p></div>';
            
            const tests = [
                {
                    name: 'JSON Parsing Test',
                    test: () => {
                        try {
                            const testObj = { test: "success", arabic: "نجح" };
                            const jsonStr = JSON.stringify(testObj);
                            const parsed = JSON.parse(jsonStr);
                            return { success: true, message: "JSON parsing working correctly" };
                        } catch (error) {
                            return { success: false, message: "JSON parsing failed: " + error.message };
                        }
                    }
                },
                {
                    name: 'Fetch API Test',
                    test: async () => {
                        try {
                            // Test with a simple endpoint
                            const response = await fetch('data:application/json,{"test":"success"}');
                            const data = await response.json();
                            return { success: true, message: "Fetch API working correctly" };
                        } catch (error) {
                            return { success: false, message: "Fetch API failed: " + error.message };
                        }
                    }
                },
                {
                    name: 'Database API Test',
                    test: async () => {
                        try {
                            const response = await fetch('../php/api/test-connection.php');
                            const data = await response.json();
                            
                            if (data && data.success) {
                                const mockNote = mockEnabled ? " (Mock)" : "";
                                return { 
                                    success: true, 
                                    message: `✅ Database connection successful${mockNote}. DB: ${data.database_info?.name || 'unknown'}` 
                                };
                            } else {
                                return { success: false, message: `❌ Database connection failed: ${data?.message || 'Unknown error'}` };
                            }
                        } catch (error) {
                            return { success: false, message: `❌ API call failed: ${error.message}` };
                        }
                    }
                },
                {
                    name: 'Roles API Test',
                    test: async () => {
                        try {
                            const response = await fetch('../php/api/test-roles.php');
                            const data = await response.json();
                            
                            if (data && data.success && Array.isArray(data.roles)) {
                                const mockNote = mockEnabled ? " (Mock)" : "";
                                return { 
                                    success: true, 
                                    message: `✅ Roles API working${mockNote}. Found ${data.roles.length} roles` 
                                };
                            } else {
                                return { success: false, message: `❌ Roles API failed: ${data?.message || 'Invalid response format'}` };
                            }
                        } catch (error) {
                            return { success: false, message: `❌ Roles API call failed: ${error.message}` };
                        }
                    }
                }
            ];
            
            let html = '<h4>نتائج الاختبارات:</h4>';
            let successCount = 0;
            
            for (const test of tests) {
                try {
                    const result = await test.test();
                    const cssClass = result.success ? 'test-success' : 'test-error';
                    const icon = result.success ? '✅' : '❌';
                    
                    if (result.success) successCount++;
                    
                    html += `
                        <div class="test-result ${cssClass}">
                            <h6>${icon} ${test.name}</h6>
                            <p>${result.message}</p>
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="test-result test-error">
                            <h6>❌ ${test.name}</h6>
                            <p>Test execution failed: ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            // Summary
            const successRate = Math.round((successCount / tests.length) * 100);
            const summaryClass = successRate >= 75 ? 'test-success' : 'test-error';
            
            html = `
                <div class="test-result ${summaryClass}">
                    <h5>📊 ملخص النتائج</h5>
                    <p>معدل النجاح: ${successRate}% (${successCount}/${tests.length})</p>
                    ${mockEnabled ? '<p><small>🔧 Mock Server Active - للنتائج الحقيقية استخدم خادم PHP</small></p>' : ''}
                </div>
            ` + html;
            
            resultsDiv.innerHTML = html;
        }
        
        // Event listeners
        document.getElementById('enable-mock').addEventListener('click', enableMockServer);
        document.getElementById('run-tests').addEventListener('click', runTests);
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            displayBrowserInfo();
        });
    </script>
</body>
</html>
