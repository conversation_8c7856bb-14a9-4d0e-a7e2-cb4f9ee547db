<?php
header('Content-Type: application/json; charset=utf-8');

try {
    require_once __DIR__ . '/php/config.php';
    
    // Test the exact query from products-multi-user.php
    $sql = "
        SELECT p.*, u.username as owner_username, u.email as owner_email 
        FROM produits p 
        LEFT JOIN users u ON p.user_id = u.id 
        ORDER BY p.created_at DESC
        LIMIT 5
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Query executed successfully',
        'products_count' => count($products),
        'first_product' => $products[0] ?? null
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
}
?>