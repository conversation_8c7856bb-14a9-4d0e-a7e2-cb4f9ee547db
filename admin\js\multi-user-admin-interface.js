/**
 * Multi-User Admin Interface
 * Enhanced admin interface with user isolation and role-based access
 */

(function() {
    'use strict';

    console.log('🔧 Multi-User Admin Interface loading...');

    // Configuration
    const config = {
        currentUserId: 1, // Default to demo user
        currentUserRole: 'admin', // Default to admin
        apiBaseUrl: '/php/api/',
        refreshInterval: 30000 // 30 seconds
    };

    // Enhanced dashboard loading with multi-user support
    function loadEnhancedDashboard() {
        console.log('📊 Loading enhanced multi-user dashboard...');

        const dashboardContainer = document.getElementById('dashboard');
        if (!dashboardContainer) {
            console.error('Dashboard container not found');
            return;
        }

        // Show loading state
        showDashboardLoading();

        // Load admin overview
        fetch(`${config.apiBaseUrl}admin-dashboard.php?action=overview&user_id=${config.currentUserId}&user_role=${config.currentUserRole}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayEnhancedDashboard(data.data);
                    console.log('✅ Enhanced dashboard loaded successfully');
                } else {
                    throw new Error(data.message || 'Failed to load dashboard');
                }
            })
            .catch(error => {
                console.error('❌ Error loading enhanced dashboard:', error);
                showDashboardError(error.message);
            });
    }

    // Enhanced products loading with ownership info
    function loadEnhancedProducts() {
        console.log('📦 Loading enhanced products with ownership info...');

        const productsContainer = document.getElementById('books');
        if (!productsContainer) {
            console.error('Products container not found');
            return;
        }

        // Show loading state
        showProductsLoading();

        // Load products with multi-user support
        fetch(`${config.apiBaseUrl}products-multi-user.php?user_id=${config.currentUserId}&user_role=${config.currentUserRole}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayEnhancedProducts(data.data, data.stats, data.user_info);
                    console.log(`✅ Enhanced products loaded: ${data.count} products`);
                } else {
                    throw new Error(data.message || 'Failed to load products');
                }
            })
            .catch(error => {
                console.error('❌ Error loading enhanced products:', error);
                showProductsError(error.message);
            });
    }

    // Display enhanced dashboard
    function displayEnhancedDashboard(data) {
        const dashboardContainer = document.getElementById('dashboard');
        if (!dashboardContainer) return;

        const isAdmin = config.currentUserRole === 'admin';

        let dashboardHTML = `
            <div class="enhanced-dashboard">
                <div class="dashboard-header">
                    <h2><i class="fas fa-tachometer-alt"></i> ${isAdmin ? 'لوحة تحكم المدير' : 'لوحة التحكم'}</h2>
                    <div class="user-info">
                        <span class="user-role ${isAdmin ? 'admin' : 'seller'}">${isAdmin ? 'مدير النظام' : 'بائع'}</span>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-content">
                            <h3>${data.system_stats.total_users}</h3>
                            <p>إجمالي المستخدمين</p>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon"><i class="fas fa-box"></i></div>
                        <div class="stat-content">
                            <h3>${data.system_stats.total_products}</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon"><i class="fas fa-bullhorn"></i></div>
                        <div class="stat-content">
                            <h3>${data.system_stats.total_landing_pages}</h3>
                            <p>صفحات الهبوط</p>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="stat-content">
                            <h3>${data.system_stats.total_orders}</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                </div>
        `;

        if (isAdmin && data.top_sellers && data.top_sellers.length > 0) {
            dashboardHTML += `
                <div class="admin-section">
                    <h3><i class="fas fa-crown"></i> أفضل البائعين</h3>
                    <div class="sellers-grid">
            `;

            data.top_sellers.forEach(seller => {
                dashboardHTML += `
                    <div class="seller-card">
                        <div class="seller-info">
                            <h4>${seller.first_name} ${seller.last_name}</h4>
                            <p>@${seller.username}</p>
                            <small>${seller.email}</small>
                        </div>
                        <div class="seller-stats">
                            <span class="stat">${seller.product_count} منتج</span>
                            <span class="stat">${seller.landing_page_count} صفحة هبوط</span>
                        </div>
                    </div>
                `;
            });

            dashboardHTML += `
                    </div>
                </div>
            `;
        }

        if (data.recent_activity && data.recent_activity.length > 0) {
            dashboardHTML += `
                <div class="recent-activity">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <div class="activity-list">
            `;

            data.recent_activity.slice(0, 10).forEach(activity => {
                const icon = activity.type === 'product' ? 'box' :
                            activity.type === 'landing_page' ? 'bullhorn' : 'shopping-cart';
                const typeText = activity.type === 'product' ? 'منتج' :
                               activity.type === 'landing_page' ? 'صفحة هبوط' : 'طلب';

                dashboardHTML += `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>${activity.title}</strong></p>
                            <small>${typeText} بواسطة ${activity.user} - ${formatDate(activity.date)}</small>
                        </div>
                    </div>
                `;
            });

            dashboardHTML += `
                    </div>
                </div>
            `;
        }

        dashboardHTML += `
                <div class="quick-actions">
                    <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                    <div class="actions-grid">
                        <button class="action-btn primary" onclick="showSection('books')">
                            <i class="fas fa-plus"></i> إدارة المنتجات
                        </button>
                        <button class="action-btn success" onclick="showSection('landingPages')">
                            <i class="fas fa-bullhorn"></i> صفحات الهبوط
                        </button>
                        <button class="action-btn info" onclick="showSection('orders')">
                            <i class="fas fa-shopping-cart"></i> الطلبات
                        </button>
                        <button class="action-btn warning" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </button>
                    </div>
                </div>
            </div>
        `;

        dashboardContainer.innerHTML = dashboardHTML;
    }

    // Display enhanced products with ownership info
    function displayEnhancedProducts(products, stats, userInfo) {
        const productsContainer = document.getElementById('books');
        if (!productsContainer) return;

        const isAdmin = userInfo.is_admin;

        let productsHTML = `
            <div class="enhanced-products">
                <div class="products-header">
                    <h2><i class="fas fa-box"></i> ${isAdmin ? 'جميع المنتجات (عرض المدير)' : 'منتجاتي'}</h2>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>

                <div class="products-stats">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card success">
                                <h4>${stats.total_products || products.length}</h4>
                                <p>إجمالي المنتجات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card info">
                                <h4>${stats.active_products || products.filter(p => p.actif == 1).length}</h4>
                                <p>منتجات نشطة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card warning">
                                <h4>${stats.products_with_landing_pages || products.filter(p => p.has_landing_page).length}</h4>
                                <p>لها صفحات هبوط</p>
                            </div>
                        </div>
                        ${isAdmin ? `
                        <div class="col-md-3">
                            <div class="stat-card primary">
                                <h4>${stats.total_sellers || 1}</h4>
                                <p>إجمالي البائعين</p>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
        `;

        if (products.length === 0) {
            productsHTML += `
                <div class="empty-state">
                    <i class="fas fa-box-open fa-3x text-muted"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>${isAdmin ? 'لا توجد منتجات في النظام' : 'لم تقم بإضافة أي منتجات بعد'}</p>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>
            `;
        } else {
            productsHTML += `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                ${isAdmin ? '<th>المالك</th>' : ''}
                                <th>صفحة الهبوط</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            products.forEach(product => {
                const statusBadge = product.actif == 1 ?
                    '<span class="badge bg-success">نشط</span>' :
                    '<span class="badge bg-secondary">غير نشط</span>';

                const landingPageBadge = product.has_landing_page ?
                    '<span class="badge bg-info">متوفرة</span>' :
                    '<span class="badge bg-warning">غير متوفرة</span>';

                const imageUrl = product.image_url || '/images/placeholder.jpg';

                productsHTML += `
                    <tr>
                        <td>
                            <img src="${imageUrl}" alt="${product.titre}"
                                 style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"
                                 onerror="this.src='/images/placeholder.jpg'">
                        </td>
                        <td>
                            <strong>${product.titre}</strong>
                            <br><small class="text-muted">${product.description ? product.description.substring(0, 50).replace(/<[^>]*>/g, '') + '...' : ''}</small>
                        </td>
                        <td><strong>${product.formatted_price}</strong></td>
                        <td>
                            <span class="badge ${product.stock > 10 ? 'bg-success' : product.stock > 0 ? 'bg-warning' : 'bg-danger'}">
                                ${product.stock}
                            </span>
                        </td>
                        <td>${statusBadge}</td>
                `;

                if (isAdmin && product.ownership_info) {
                    productsHTML += `
                        <td>
                            <div class="owner-info">
                                <strong>${product.ownership_info.full_name}</strong>
                                <br><small>@${product.ownership_info.username}</small>
                                <br><span class="badge bg-primary">${product.ownership_info.role}</span>
                            </div>
                        </td>
                    `;
                }

                productsHTML += `
                        <td>${landingPageBadge}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewProduct(${product.id})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${product.has_landing_page ?
                                    `<button class="btn btn-sm btn-outline-success" onclick="viewLandingPage(${product.id})" title="صفحة الهبوط">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>` :
                                    `<button class="btn btn-sm btn-outline-warning" onclick="createLandingPage(${product.id})" title="إنشاء صفحة هبوط">
                                        <i class="fas fa-plus"></i>
                                    </button>`
                                }
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            productsHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        productsHTML += `
                <div class="products-footer">
                    <button class="btn btn-outline-secondary" onclick="window.multiUserAdmin.loadProducts()">
                        <i class="fas fa-redo"></i> إعادة تحميل
                    </button>
                </div>
            </div>
        `;

        productsContainer.innerHTML = productsHTML;
    }

    // Utility functions
    function showDashboardLoading() {
        const dashboardContainer = document.getElementById('dashboard');
        if (dashboardContainer) {
            dashboardContainer.innerHTML = `
                <div class="loading-container text-center" style="padding: 40px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h3 style="margin-top: 20px;">جاري تحميل لوحة التحكم...</h3>
                </div>
            `;
        }
    }

    function showProductsLoading() {
        const productsContainer = document.getElementById('books');
        if (productsContainer) {
            productsContainer.innerHTML = `
                <div class="loading-container text-center" style="padding: 40px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h3 style="margin-top: 20px;">جاري تحميل المنتجات...</h3>
                </div>
            `;
        }
    }

    function showDashboardError(message) {
        const dashboardContainer = document.getElementById('dashboard');
        if (dashboardContainer) {
            dashboardContainer.innerHTML = `
                <div class="error-container text-center" style="padding: 40px;">
                    <div class="alert alert-danger">
                        <h4>❌ خطأ في تحميل لوحة التحكم</h4>
                        <p>${message}</p>
                        <button class="btn btn-outline-danger" onclick="window.multiUserAdmin.loadDashboard()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function showProductsError(message) {
        const productsContainer = document.getElementById('books');
        if (productsContainer) {
            productsContainer.innerHTML = `
                <div class="error-container text-center" style="padding: 40px;">
                    <div class="alert alert-danger">
                        <h4>❌ خطأ في تحميل المنتجات</h4>
                        <p>${message}</p>
                        <button class="btn btn-outline-danger" onclick="window.multiUserAdmin.loadProducts()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Public API
    window.multiUserAdmin = {
        loadDashboard: loadEnhancedDashboard,
        loadProducts: loadEnhancedProducts,
        config: config
    };

    // Override existing functions
    if (typeof window.loadProducts === 'function') {
        const originalLoadProducts = window.loadProducts;
        window.loadProducts = function() {
            console.log('🔄 Using enhanced multi-user products loader');
            loadEnhancedProducts();
        };
    } else {
        window.loadProducts = loadEnhancedProducts;
    }

    // Auto-initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ Multi-User Admin Interface ready');

        // Check if enhanced dashboard already exists before loading our version
        const dashboardSection = document.getElementById('dashboard');
        if (dashboardSection && dashboardSection.classList.contains('active')) {
            const enhancedDashboard = dashboardSection.querySelector('.dashboard-header');
            if (!enhancedDashboard) {
                setTimeout(loadEnhancedDashboard, 1000);
            } else {
                console.log('✅ Enhanced dashboard already present - skipping multi-user dashboard');
            }
        }

        // Load enhanced products if we're on products section
        const productsSection = document.getElementById('books');
        if (productsSection && productsSection.classList.contains('active')) {
            setTimeout(loadEnhancedProducts, 1000);
        }
    });

    console.log('✅ Multi-User Admin Interface loaded successfully');

})();
