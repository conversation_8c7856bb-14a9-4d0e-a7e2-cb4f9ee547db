<?php
// Simple debug test for products-multi-user.php
header('Content-Type: application/json; charset=utf-8');

try {
    // Test 1: Check if config.php exists and loads
    if (!file_exists('php/config.php')) {
        throw new Exception('config.php not found');
    }
    
    require_once 'php/config.php';
    
    // Test 2: Check database connection
    if (!isset($conn) || !$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Test 3: Check if users table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'users'");
    $stmt->execute();
    $userTableExists = $stmt->rowCount() > 0;
    
    if (!$userTableExists) {
        throw new Exception('Users table does not exist');
    }
    
    // Test 4: Check if user with ID 1 exists
    $stmt = $conn->prepare("SELECT id, role, store_id FROM users WHERE id = 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        // Create a test user if none exists
        $stmt = $conn->prepare("INSERT INTO users (id, username, email, role, store_id) VALUES (1, 'admin', '<EMAIL>', 'admin', 1)");
        $stmt->execute();
        
        $stmt = $conn->prepare("SELECT id, role, store_id FROM users WHERE id = 1");
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Test 5: Check if produits table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'produits'");
    $stmt->execute();
    $produitsTableExists = $stmt->rowCount() > 0;
    
    if (!$produitsTableExists) {
        throw new Exception('Produits table does not exist');
    }
    
    // Test 6: Get sample products
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM produits");
    $stmt->execute();
    $productCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo json_encode([
        'success' => true,
        'tests' => [
            'config_loaded' => true,
            'database_connected' => true,
            'users_table_exists' => $userTableExists,
            'user_found' => $user !== false,
            'produits_table_exists' => $produitsTableExists,
            'product_count' => $productCount
        ],
        'user_info' => $user,
        'message' => 'All tests passed'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>