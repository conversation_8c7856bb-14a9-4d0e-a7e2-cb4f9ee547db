<?php
/**
 * Roles API - Fixed Version
 * Handles all role-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$dbname = 'poultraydz';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';

try {
    switch ($action) {
        case 'list':
            handleListRoles($pdo);
            break;
        case 'create':
            handleCreateRole($pdo);
            break;
        case 'update':
            handleUpdateRole($pdo);
            break;
        case 'delete':
            handleDeleteRole($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListRoles($pdo) {
    try {
        // Create roles table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                permissions JSON,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $count = $countStmt->fetch()['count'];
        
        if ($count == 0) {
            $sampleRoles = [
                [
                    'name' => 'مدير عام',
                    'description' => 'صلاحيات كاملة للنظام',
                    'permissions' => json_encode(['all'])
                ],
                [
                    'name' => 'مدير المتاجر',
                    'description' => 'إدارة المتاجر والمنتجات',
                    'permissions' => json_encode(['stores', 'products', 'orders'])
                ],
                [
                    'name' => 'مدير المحتوى',
                    'description' => 'إدارة المحتوى والصفحات',
                    'permissions' => json_encode(['content', 'pages', 'categories'])
                ],
                [
                    'name' => 'مشرف الدعم',
                    'description' => 'دعم العملاء والمساعدة',
                    'permissions' => json_encode(['support', 'tickets', 'users'])
                ],
                [
                    'name' => 'مستخدم عادي',
                    'description' => 'صلاحيات أساسية للمستخدمين',
                    'permissions' => json_encode(['profile', 'orders'])
                ]
            ];
            
            $insertStmt = $pdo->prepare("INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)");
            foreach ($sampleRoles as $role) {
                $insertStmt->execute([$role['name'], $role['description'], $role['permissions']]);
            }
        }

        // Fetch all roles
        $stmt = $pdo->query("SELECT * FROM roles ORDER BY created_at DESC");
        $roles = $stmt->fetchAll();

        // Decode permissions JSON for each role
        foreach ($roles as &$role) {
            $role['permissions'] = json_decode($role['permissions'], true) ?? [];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'roles' => $roles,
                'total' => count($roles)
            ],
            'message' => 'Roles retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve roles: ' . $e->getMessage());
    }
}

function handleCreateRole($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Role name is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO roles (name, description, permissions, status) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            json_encode($input['permissions'] ?? []),
            $input['status'] ?? 'active'
        ]);

        $roleId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $roleId,
                'name' => $input['name']
            ],
            'message' => 'Role created successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create role: ' . $e->getMessage());
    }
}

function handleUpdateRole($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id']) || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Role ID and name are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("UPDATE roles SET name = ?, description = ?, permissions = ?, status = ? WHERE id = ?");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            json_encode($input['permissions'] ?? []),
            $input['status'] ?? 'active',
            $input['id']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $input['id'],
                'name' => $input['name']
            ],
            'message' => 'Role updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update role: ' . $e->getMessage());
    }
}

function handleDeleteRole($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Role ID is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM roles WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Role deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete role: ' . $e->getMessage());
    }
}
?>
