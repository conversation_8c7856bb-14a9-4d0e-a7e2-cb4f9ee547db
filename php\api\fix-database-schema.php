<?php
/**
 * Fix Database Schema
 * Adds missing columns and fixes database issues
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes = [];
    $errors = [];
    
    // Fix 1: Add display_name_ar column to roles table if missing
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM roles LIKE 'display_name_ar'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE roles ADD COLUMN display_name_ar VARCHAR(100) DEFAULT '' AFTER display_name");
            $fixes[] = "Added display_name_ar column to roles table";
            
            // Update existing roles with Arabic names
            $updates = [
                'admin' => 'مدير النظام',
                'seller' => 'بائع',
                'user' => 'مستخدم',
                'moderator' => 'مشرف',
                'editor' => 'محرر'
            ];
            
            foreach ($updates as $role => $arabicName) {
                $stmt = $pdo->prepare("UPDATE roles SET display_name_ar = ? WHERE name = ?");
                $stmt->execute([$arabicName, $role]);
            }
            
            $fixes[] = "Updated existing roles with Arabic names";
        } else {
            $fixes[] = "display_name_ar column already exists in roles table";
        }
    } catch (Exception $e) {
        $errors[] = "Error fixing roles table: " . $e->getMessage();
    }
    
    // Fix 2: Add missing columns to users table if needed
    try {
        $columns_to_check = [
            'display_name' => "VARCHAR(100) DEFAULT ''",
            'avatar' => "VARCHAR(255) DEFAULT NULL",
            'phone' => "VARCHAR(20) DEFAULT NULL",
            'address' => "TEXT DEFAULT NULL",
            'last_login' => "TIMESTAMP NULL DEFAULT NULL",
            'is_active' => "TINYINT(1) DEFAULT 1"
        ];
        
        foreach ($columns_to_check as $column => $definition) {
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE '$column'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE users ADD COLUMN $column $definition");
                $fixes[] = "Added $column column to users table";
            }
        }
    } catch (Exception $e) {
        $errors[] = "Error fixing users table: " . $e->getMessage();
    }
    
    // Fix 3: Add missing columns to products table if needed
    try {
        $product_columns = [
            'has_landing_page' => "TINYINT(1) DEFAULT 0",
            'landing_page_url' => "VARCHAR(255) DEFAULT NULL",
            'meta_title' => "VARCHAR(255) DEFAULT NULL",
            'meta_description' => "TEXT DEFAULT NULL",
            'tags' => "TEXT DEFAULT NULL",
            'featured' => "TINYINT(1) DEFAULT 0",
            'sort_order' => "INT DEFAULT 0"
        ];
        
        foreach ($product_columns as $column => $definition) {
            $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE '$column'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE products ADD COLUMN $column $definition");
                $fixes[] = "Added $column column to products table";
            }
        }
    } catch (Exception $e) {
        $errors[] = "Error fixing products table: " . $e->getMessage();
    }
    
    // Fix 4: Create missing tables if needed
    try {
        // Check if landing_pages table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'landing_pages'");
        if ($stmt->rowCount() == 0) {
            $sql = "CREATE TABLE landing_pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                content TEXT,
                meta_title VARCHAR(255),
                meta_description TEXT,
                template VARCHAR(50) DEFAULT 'default',
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )";
            $pdo->exec($sql);
            $fixes[] = "Created landing_pages table";
        }
        
        // Check if categories table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
        if ($stmt->rowCount() == 0) {
            $sql = "CREATE TABLE categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                name_ar VARCHAR(100) DEFAULT '',
                description TEXT,
                parent_id INT DEFAULT NULL,
                is_active TINYINT(1) DEFAULT 1,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
            )";
            $pdo->exec($sql);
            $fixes[] = "Created categories table";
        }
        
        // Check if orders table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
        if ($stmt->rowCount() == 0) {
            $sql = "CREATE TABLE orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                total DECIMAL(10,2) NOT NULL,
                status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                customer_name VARCHAR(100) NOT NULL,
                customer_email VARCHAR(100),
                customer_phone VARCHAR(20),
                customer_address TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            )";
            $pdo->exec($sql);
            $fixes[] = "Created orders table";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error creating missing tables: " . $e->getMessage();
    }
    
    // Fix 5: Update existing data to prevent null issues
    try {
        // Update roles with empty display_name_ar
        $pdo->exec("UPDATE roles SET display_name_ar = display_name WHERE display_name_ar IS NULL OR display_name_ar = ''");
        $fixes[] = "Updated roles with missing Arabic display names";
        
        // Update users with missing display_name
        $pdo->exec("UPDATE users SET display_name = username WHERE display_name IS NULL OR display_name = ''");
        $fixes[] = "Updated users with missing display names";
        
    } catch (Exception $e) {
        $errors[] = "Error updating existing data: " . $e->getMessage();
    }
    
    // Return results
    echo json_encode([
        'success' => true,
        'message' => 'Database schema fixes completed',
        'fixes' => $fixes,
        'errors' => $errors,
        'total_fixes' => count($fixes),
        'total_errors' => count($errors)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
