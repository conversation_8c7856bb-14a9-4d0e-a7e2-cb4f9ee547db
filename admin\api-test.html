<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: -30px -30px 30px -30px;
            padding: 30px;
            border-radius: 12px 12px 0 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .test-all-button {
            background: #10b981;
        }
        .test-all-button:hover {
            background: #059669;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> API Test Suite</h1>
            <p>اختبار شامل لجميع نقاط النهاية API</p>
            <button class="test-button test-all-button" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
        </div>

        <!-- Database Connection Test -->
        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبار الاتصال بقاعدة البيانات</h3>
            <button class="test-button" onclick="testDatabaseConnection()">
                <i class="fas fa-play"></i> اختبار الاتصال
            </button>
            <span class="status-indicator status-pending" id="db-status"></span>
            <div id="dbTestResult"></div>
        </div>

        <!-- Categories API Test -->
        <div class="test-section">
            <h3><i class="fas fa-tags"></i> اختبار API الفئات</h3>
            <button class="test-button" onclick="testCategoriesAPI()">
                <i class="fas fa-play"></i> اختبار الفئات
            </button>
            <span class="status-indicator status-pending" id="categories-status"></span>
            <div id="categoriesTestResult"></div>
        </div>

        <!-- Users API Test -->
        <div class="test-section">
            <h3><i class="fas fa-users"></i> اختبار API المستخدمين</h3>
            <button class="test-button" onclick="testUsersAPI()">
                <i class="fas fa-play"></i> اختبار المستخدمين
            </button>
            <span class="status-indicator status-pending" id="users-status"></span>
            <div id="usersTestResult"></div>
        </div>

        <!-- Roles API Test -->
        <div class="test-section">
            <h3><i class="fas fa-user-shield"></i> اختبار API الأدوار</h3>
            <button class="test-button" onclick="testRolesAPI()">
                <i class="fas fa-play"></i> اختبار الأدوار
            </button>
            <span class="status-indicator status-pending" id="roles-status"></span>
            <div id="rolesTestResult"></div>
        </div>

        <!-- Security Settings API Test -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> اختبار API إعدادات الأمان</h3>
            <button class="test-button" onclick="testSecurityAPI()">
                <i class="fas fa-play"></i> اختبار الأمان
            </button>
            <span class="status-indicator status-pending" id="security-status"></span>
            <div id="securityTestResult"></div>
        </div>

        <!-- Subscriptions API Test -->
        <div class="test-section">
            <h3><i class="fas fa-credit-card"></i> اختبار API الاشتراكات</h3>
            <button class="test-button" onclick="testSubscriptionsAPI()">
                <i class="fas fa-play"></i> اختبار الاشتراكات
            </button>
            <span class="status-indicator status-pending" id="subscriptions-status"></span>
            <div id="subscriptionsTestResult"></div>
        </div>

        <!-- Overall Results -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> النتائج الإجمالية</h3>
            <div id="overallResults"></div>
        </div>
    </div>

    <script>
        const testResults = {};

        async function testAPI(url, testName, statusId, resultId) {
            const statusIndicator = document.getElementById(statusId);
            const resultDiv = document.getElementById(resultId);
            
            statusIndicator.className = 'status-indicator status-pending';
            resultDiv.innerHTML = '<div class="info">جاري الاختبار...</div>';

            try {
                const response = await fetch(url);
                const data = await response.json();

                if (response.ok && data.success) {
                    statusIndicator.className = 'status-indicator status-success';
                    resultDiv.innerHTML = `
                        <div class="success">✅ ${testName} - نجح الاختبار</div>
                        <div class="info">الاستجابة: ${JSON.stringify(data, null, 2)}</div>
                    `;
                    testResults[testName] = { success: true, data };
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator status-error';
                resultDiv.innerHTML = `
                    <div class="error">❌ ${testName} - فشل الاختبار</div>
                    <div class="error">الخطأ: ${error.message}</div>
                `;
                testResults[testName] = { success: false, error: error.message };
            }
        }

        async function testDatabaseConnection() {
            await testAPI('php/api/categories-fixed.php?action=list', 'اتصال قاعدة البيانات', 'db-status', 'dbTestResult');
        }

        async function testCategoriesAPI() {
            await testAPI('php/api/categories-fixed.php?action=list', 'API الفئات', 'categories-status', 'categoriesTestResult');
        }

        async function testUsersAPI() {
            await testAPI('php/api/users.php?action=list', 'API المستخدمين', 'users-status', 'usersTestResult');
        }

        async function testRolesAPI() {
            await testAPI('php/api/roles-fixed.php?action=list', 'API الأدوار', 'roles-status', 'rolesTestResult');
        }

        async function testSecurityAPI() {
            await testAPI('php/api/security-settings.php?action=dashboard', 'API الأمان', 'security-status', 'securityTestResult');
        }

        async function testSubscriptionsAPI() {
            await testAPI('php/api/subscriptions-fixed.php?action=plans', 'API الاشتراكات', 'subscriptions-status', 'subscriptionsTestResult');
        }

        async function runAllTests() {
            console.log('🚀 بدء تشغيل جميع الاختبارات...');
            
            await testDatabaseConnection();
            await testCategoriesAPI();
            await testUsersAPI();
            await testRolesAPI();
            await testSecurityAPI();
            await testSubscriptionsAPI();

            // Show overall results
            const overallDiv = document.getElementById('overallResults');
            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(r => r.success).length;
            const failedTests = totalTests - successfulTests;

            const successRate = totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0;

            overallDiv.innerHTML = `
                <div class="info">
                    إجمالي الاختبارات: ${totalTests}
                    الناجحة: ${successfulTests}
                    الفاشلة: ${failedTests}
                    معدل النجاح: ${successRate}%
                </div>
                ${successRate == 100 ? 
                    '<div class="success">🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.</div>' :
                    '<div class="warning">⚠️ بعض الاختبارات فشلت. يرجى مراجعة التفاصيل أعلاه.</div>'
                }
            `;

            console.log('✅ انتهت جميع الاختبارات');
            console.log('النتائج:', testResults);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🔧 API Test Suite loaded');
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
