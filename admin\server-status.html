<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 حالة الخادم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .status-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .status-item {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .status-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .solution-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <h1 class="text-center mb-4">🔧 تشخيص حالة الخادم</h1>
        
        <div class="text-center mb-4">
            <button id="test-server" class="btn btn-primary btn-lg">🚀 اختبار الخادم</button>
        </div>
        
        <div id="results"></div>
        
        <div class="solution-box">
            <h4>🛠️ الحلول المقترحة:</h4>
            
            <div class="alert alert-warning">
                <h6><strong>إذا كانت ملفات PHP تتحمل بدلاً من التنفيذ:</strong></h6>
                <p>هذا يعني أن الخادم لا يعالج ملفات PHP. الحلول:</p>
                
                <div class="code-block">
                    <strong>الحل 1: استخدام خادم PHP المدمج</strong><br>
                    # أوقف الخادم الحالي واستخدم:<br>
                    php -S localhost:8000
                </div>
                
                <div class="code-block">
                    <strong>الحل 2: استخدام XAMPP/WAMP</strong><br>
                    - تأكد من تشغيل Apache<br>
                    - تأكد من تشغيل MySQL<br>
                    - استخدم http://localhost/your-project
                </div>
            </div>
            
            <div class="alert alert-info">
                <h6><strong>إذا كانت قاعدة البيانات لا تعمل:</strong></h6>
                <p>تحقق من:</p>
                <ul>
                    <li>تشغيل MySQL/MariaDB على المنفذ 3307</li>
                    <li>وجود قاعدة البيانات 'mossab-landing-page'</li>
                    <li>صحة بيانات المستخدم (root بدون كلمة مرور)</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testServer() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>جاري اختبار الخادم...</p></div>';
            
            const tests = [
                {
                    name: 'اختبار معالجة PHP',
                    url: '../php/api/test-connection.php',
                    expectedType: 'application/json'
                },
                {
                    name: 'اختبار قاعدة البيانات',
                    url: '../php/api/web-diagnostic.php',
                    expectedType: 'text/plain'
                }
            ];
            
            let html = '<h4>نتائج الاختبار:</h4>';
            let phpWorking = false;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const contentType = response.headers.get('content-type') || '';
                    const text = await response.text();
                    
                    let success = false;
                    let message = '';
                    let cssClass = 'status-error';
                    let icon = '❌';
                    
                    if (response.ok) {
                        if (contentType.includes(test.expectedType)) {
                            success = true;
                            phpWorking = true;
                            cssClass = 'status-success';
                            icon = '✅';
                            message = `الخادم يعالج PHP بشكل صحيح - Content-Type: ${contentType}`;
                        } else if (contentType.includes('application/json')) {
                            // JSON response but might have errors
                            try {
                                const data = JSON.parse(text);
                                if (data.success) {
                                    success = true;
                                    phpWorking = true;
                                    cssClass = 'status-success';
                                    icon = '✅';
                                    message = `API يعمل بشكل صحيح: ${data.message}`;
                                } else {
                                    cssClass = 'status-warning';
                                    icon = '⚠️';
                                    message = `API يعمل لكن هناك خطأ: ${data.message || data.error}`;
                                }
                            } catch (e) {
                                message = `استجابة JSON غير صالحة`;
                            }
                        } else {
                            message = `الخادم لا يعالج PHP - يتم إرجاع: ${contentType}`;
                            if (text.includes('<?php')) {
                                message += ' (كود PHP خام)';
                            }
                        }
                    } else {
                        message = `خطأ HTTP: ${response.status} ${response.statusText}`;
                    }
                    
                    html += `
                        <div class="status-item ${cssClass}">
                            <h6>${icon} ${test.name}</h6>
                            <p><strong>النتيجة:</strong> ${message}</p>
                            <p><strong>URL:</strong> ${test.url}</p>
                            <p><strong>Content-Type:</strong> ${contentType}</p>
                        </div>
                    `;
                    
                } catch (error) {
                    html += `
                        <div class="status-item status-error">
                            <h6>❌ ${test.name}</h6>
                            <p><strong>خطأ:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            // Summary
            if (phpWorking) {
                html += `
                    <div class="status-item status-success">
                        <h5>🎉 الخادم يعمل بشكل صحيح!</h5>
                        <p>يمكنك الآن اختبار Firefox compatibility:</p>
                        <a href="firefox-test.html" class="btn btn-success">اختبار Firefox</a>
                    </div>
                `;
            } else {
                html += `
                    <div class="status-item status-error">
                        <h5>⚠️ الخادم لا يعالج ملفات PHP</h5>
                        <p>يرجى اتباع الحلول المقترحة أعلاه</p>
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        document.getElementById('test-server').addEventListener('click', testServer);
        
        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testServer, 1000);
        });
    </script>
</body>
</html>
