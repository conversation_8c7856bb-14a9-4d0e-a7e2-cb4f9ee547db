<?php
/**
 * Security Settings API
 * Handles all security-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$dbname = 'poultraydz';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'dashboard';

try {
    switch ($action) {
        case 'dashboard':
            handleSecurityDashboard($pdo);
            break;
        case 'password_policy':
            handlePasswordPolicy($pdo);
            break;
        case 'threat_detection':
            handleThreatDetection($pdo);
            break;
        case 'update_settings':
            handleUpdateSettings($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleSecurityDashboard($pdo) {
    try {
        // Create security_settings table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS security_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) NOT NULL UNIQUE,
                setting_value JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Initialize default settings if they don't exist
        $defaultSettings = [
            'security_dashboard' => [
                'active_sessions' => 24,
                'failed_logins' => 3,
                'blocked_ips' => 2,
                'security_alerts' => 1,
                'last_scan' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($defaultSettings as $key => $value) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO security_settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, json_encode($value)]);
        }

        // Fetch dashboard data
        $stmt = $pdo->prepare("SELECT setting_value FROM security_settings WHERE setting_key = ?");
        $stmt->execute(['security_dashboard']);
        $result = $stmt->fetch();

        $dashboardData = $result ? json_decode($result['setting_value'], true) : $defaultSettings['security_dashboard'];

        echo json_encode([
            'success' => true,
            'data' => $dashboardData,
            'message' => 'Security dashboard data retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve security dashboard: ' . $e->getMessage());
    }
}

function handlePasswordPolicy($pdo) {
    try {
        // Initialize default password policy if it doesn't exist
        $defaultPolicy = [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => false,
            'password_expiry_days' => 90,
            'prevent_reuse' => 5
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO security_settings (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute(['password_policy', json_encode($defaultPolicy)]);

        // Fetch password policy
        $stmt = $pdo->prepare("SELECT setting_value FROM security_settings WHERE setting_key = ?");
        $stmt->execute(['password_policy']);
        $result = $stmt->fetch();

        $policyData = $result ? json_decode($result['setting_value'], true) : $defaultPolicy;

        echo json_encode([
            'success' => true,
            'data' => $policyData,
            'message' => 'Password policy retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve password policy: ' . $e->getMessage());
    }
}

function handleThreatDetection($pdo) {
    try {
        // Initialize default threat detection settings if they don't exist
        $defaultSettings = [
            'enable_brute_force_protection' => true,
            'max_login_attempts' => 5,
            'lockout_duration' => 30,
            'enable_ip_blocking' => true,
            'suspicious_activity_alerts' => true,
            'log_security_events' => true
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO security_settings (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute(['threat_detection', json_encode($defaultSettings)]);

        // Fetch threat detection settings
        $stmt = $pdo->prepare("SELECT setting_value FROM security_settings WHERE setting_key = ?");
        $stmt->execute(['threat_detection']);
        $result = $stmt->fetch();

        $threatData = $result ? json_decode($result['setting_value'], true) : $defaultSettings;

        echo json_encode([
            'success' => true,
            'data' => $threatData,
            'message' => 'Threat detection settings retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve threat detection settings: ' . $e->getMessage());
    }
}

function handleUpdateSettings($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['setting_key']) || !isset($input['setting_value'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Setting key and value are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO security_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$input['setting_key'], json_encode($input['setting_value'])]);

        echo json_encode([
            'success' => true,
            'message' => 'Security settings updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update security settings: ' . $e->getMessage());
    }
}
?>
