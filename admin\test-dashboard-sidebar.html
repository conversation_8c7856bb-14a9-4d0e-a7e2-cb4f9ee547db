<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>اختبار لوحة التحكم والقائمة الجانبية</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        direction: rtl;
      }

      .test-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
      }

      .test-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .test-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 30px;
      }

      .test-button {
        background: #667eea;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        margin: 5px;
        font-weight: bold;
        transition: all 0.3s ease;
      }

      .test-button:hover {
        background: #5a67d8;
        transform: translateY(-2px);
      }

      .test-result {
        margin-top: 15px;
        padding: 15px;
        border-radius: 8px;
        font-weight: bold;
      }

      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .test-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
      }

      .iframe-container {
        width: 100%;
        height: 600px;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
      }

      .iframe-container iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="test-header">
        <h1>
          <i class="fas fa-vial"></i> اختبار لوحة التحكم والقائمة الجانبية
        </h1>
        <p>فحص شامل لوظائف لوحة التحكم وأقسام القائمة الجانبية</p>
      </div>

      <div class="test-grid">
        <!-- Dashboard Test -->
        <div class="test-section">
          <h3><i class="fas fa-tachometer-alt"></i> اختبار لوحة المعلومات</h3>
          <p>فحص عرض لوحة المعلومات الرئيسية والإحصائيات</p>
          <button class="test-button" onclick="testDashboard()">
            <i class="fas fa-play"></i> اختبار لوحة المعلومات
          </button>
          <div
            id="dashboardResult"
            class="test-result"
            style="display: none"
          ></div>
        </div>

        <!-- Stores Management Test -->
        <div class="test-section">
          <h3><i class="fas fa-store"></i> اختبار إدارة المتاجر</h3>
          <p>فحص قسم إدارة المتاجر والوظائف المرتبطة</p>
          <button class="test-button" onclick="testStoresManagement()">
            <i class="fas fa-play"></i> اختبار إدارة المتاجر
          </button>
          <div
            id="storesResult"
            class="test-result"
            style="display: none"
          ></div>
        </div>

        <!-- Security Settings Test -->
        <div class="test-section">
          <h3><i class="fas fa-shield-alt"></i> اختبار إعدادات الأمان</h3>
          <p>فحص قسم إعدادات الأمان والميزات الجديدة</p>
          <button class="test-button" onclick="testSecuritySettings()">
            <i class="fas fa-play"></i> اختبار إعدادات الأمان
          </button>
          <div
            id="securityResult"
            class="test-result"
            style="display: none"
          ></div>
        </div>

        <!-- Reports Test -->
        <div class="test-section">
          <h3><i class="fas fa-chart-bar"></i> اختبار التقارير</h3>
          <p>فحص قسم التقارير والإحصائيات</p>
          <button class="test-button" onclick="testReports()">
            <i class="fas fa-play"></i> اختبار التقارير
          </button>
          <div
            id="reportsResult"
            class="test-result"
            style="display: none"
          ></div>
        </div>

        <!-- JavaScript Functions Test -->
        <div class="test-section">
          <h3><i class="fas fa-code"></i> اختبار الوظائف</h3>
          <p>فحص توفر الوظائف المطلوبة في JavaScript</p>
          <button class="test-button" onclick="testJavaScriptFunctions()">
            <i class="fas fa-play"></i> اختبار الوظائف
          </button>
          <div
            id="functionsResult"
            class="test-result"
            style="display: none"
          ></div>
        </div>

        <!-- API Endpoints Test -->
        <div class="test-section">
          <h3><i class="fas fa-plug"></i> اختبار نقاط API</h3>
          <p>فحص الاتصال بنقاط API المختلفة</p>
          <button class="test-button" onclick="testAPIEndpoints()">
            <i class="fas fa-play"></i> اختبار API
          </button>
          <div id="apiResult" class="test-result" style="display: none"></div>
        </div>
      </div>

      <!-- Live Dashboard Preview -->
      <div class="test-section">
        <h3><i class="fas fa-desktop"></i> معاينة مباشرة للوحة التحكم</h3>
        <p>معاينة مباشرة للوحة التحكم في إطار مدمج</p>
        <button class="test-button" onclick="loadDashboardPreview()">
          <i class="fas fa-eye"></i> تحميل المعاينة
        </button>
        <div id="dashboardPreview" style="display: none; margin-top: 20px">
          <div class="iframe-container">
            <iframe id="dashboardFrame" src=""></iframe>
          </div>
        </div>
      </div>
    </div>

    <script src="js/missing-functions-fix.js"></script>
    <script>
      // Test Dashboard
      function testDashboard() {
        const result = document.getElementById("dashboardResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

        setTimeout(() => {
          try {
            // Check if dashboard elements exist
            const tests = [
              {
                name: "عنصر لوحة المعلومات",
                check: () => document.getElementById("dashboard"),
              },
              {
                name: "بطاقات الإحصائيات",
                check: () => document.querySelector(".stats-grid"),
              },
              {
                name: "جدول الطلبات الحديثة",
                check: () => document.getElementById("recentOrdersTable"),
              },
            ];

            let passed = 0;
            let total = tests.length;
            let details = "";

            tests.forEach((test) => {
              if (test.check()) {
                passed++;
                details += `✅ ${test.name}<br>`;
              } else {
                details += `❌ ${test.name}<br>`;
              }
            });

            if (passed === total) {
              result.className = "test-result success";
              result.innerHTML = `🎉 نجح الاختبار! (${passed}/${total})<br>${details}`;
            } else {
              result.className = "test-result error";
              result.innerHTML = `⚠️ فشل جزئي (${passed}/${total})<br>${details}`;
            }
          } catch (error) {
            result.className = "test-result error";
            result.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
          }
        }, 1000);
      }

      // Test Stores Management
      function testStoresManagement() {
        const result = document.getElementById("storesResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

        setTimeout(() => {
          try {
            const tests = [
              {
                name: "وظيفة loadStoresManagementContent",
                check: () =>
                  typeof window.loadStoresManagementContent === "function",
              },
              {
                name: "وظيفة initializeStoresManagement",
                check: () =>
                  typeof window.initializeStoresManagement === "function",
              },
              {
                name: "عنصر storesManagementContent",
                check: () => document.getElementById("storesManagementContent"),
              },
            ];

            let passed = 0;
            let total = tests.length;
            let details = "";

            tests.forEach((test) => {
              if (test.check()) {
                passed++;
                details += `✅ ${test.name}<br>`;
              } else {
                details += `❌ ${test.name}<br>`;
              }
            });

            if (passed === total) {
              result.className = "test-result success";
              result.innerHTML = `🎉 نجح الاختبار! (${passed}/${total})<br>${details}`;
            } else {
              result.className = "test-result error";
              result.innerHTML = `⚠️ فشل جزئي (${passed}/${total})<br>${details}`;
            }
          } catch (error) {
            result.className = "test-result error";
            result.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
          }
        }, 1000);
      }

      // Test Security Settings
      function testSecuritySettings() {
        const result = document.getElementById("securityResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

        setTimeout(() => {
          try {
            const tests = [
              {
                name: "وظيفة loadSecuritySettingsContent",
                check: () =>
                  typeof window.loadSecuritySettingsContent === "function",
              },
              {
                name: "وظيفة initializeSecuritySettings",
                check: () =>
                  typeof window.initializeSecuritySettings === "function",
              },
              {
                name: "وظيفة updateSecurityScore",
                check: () => typeof window.updateSecurityScore === "function",
              },
            ];

            let passed = 0;
            let total = tests.length;
            let details = "";

            tests.forEach((test) => {
              if (test.check()) {
                passed++;
                details += `✅ ${test.name}<br>`;
              } else {
                details += `❌ ${test.name}<br>`;
              }
            });

            if (passed === total) {
              result.className = "test-result success";
              result.innerHTML = `🎉 نجح الاختبار! (${passed}/${total})<br>${details}`;
            } else {
              result.className = "test-result error";
              result.innerHTML = `⚠️ فشل جزئي (${passed}/${total})<br>${details}`;
            }
          } catch (error) {
            result.className = "test-result error";
            result.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
          }
        }, 1000);
      }

      // Test Reports
      function testReports() {
        const result = document.getElementById("reportsResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

        setTimeout(() => {
          try {
            const tests = [
              {
                name: "وظيفة loadReportsContent",
                check: () => typeof window.loadReportsContent === "function",
              },
              {
                name: "وظيفة initializeReports",
                check: () => typeof window.initializeReports === "function",
              },
              {
                name: "عنصر reportsContent",
                check: () => document.getElementById("reportsContent"),
              },
            ];

            let passed = 0;
            let total = tests.length;
            let details = "";

            tests.forEach((test) => {
              if (test.check()) {
                passed++;
                details += `✅ ${test.name}<br>`;
              } else {
                details += `❌ ${test.name}<br>`;
              }
            });

            if (passed === total) {
              result.className = "test-result success";
              result.innerHTML = `🎉 نجح الاختبار! (${passed}/${total})<br>${details}`;
            } else {
              result.className = "test-result error";
              result.innerHTML = `⚠️ فشل جزئي (${passed}/${total})<br>${details}`;
            }
          } catch (error) {
            result.className = "test-result error";
            result.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
          }
        }, 1000);
      }

      // Test JavaScript Functions
      function testJavaScriptFunctions() {
        const result = document.getElementById("functionsResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

        setTimeout(() => {
          const functions = [
            "loadStoresManagementContent",
            "loadReportsContent",
            "initializeSecuritySettings",
            "updateSecurityScore",
            "showAdminSection",
          ];

          let available = 0;
          let details = "";

          functions.forEach((func) => {
            if (typeof window[func] === "function") {
              available++;
              details += `✅ ${func}<br>`;
            } else {
              details += `❌ ${func}<br>`;
            }
          });

          if (available === functions.length) {
            result.className = "test-result success";
            result.innerHTML = `🎉 جميع الوظائف متوفرة! (${available}/${functions.length})<br>${details}`;
          } else {
            result.className = "test-result error";
            result.innerHTML = `⚠️ بعض الوظائف مفقودة (${available}/${functions.length})<br>${details}`;
          }
        }, 1000);
      }

      // Test API Endpoints
      async function testAPIEndpoints() {
        const result = document.getElementById("apiResult");
        result.style.display = "block";
        result.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري اختبار API...';

        const endpoints = [
          {
            name: "Security Settings",
            url: "php/api/security-settings.php?action=get_stats",
          },
          { name: "Demo Data", url: "php/api/demo-data.php?action=get_stats" },
          { name: "Stores API", url: "php/api/stores.php?action=get_all" },
        ];

        let results = "";
        let successful = 0;

        for (const endpoint of endpoints) {
          try {
            const response = await fetch(endpoint.url);
            if (response.ok) {
              successful++;
              results += `✅ ${endpoint.name}<br>`;
            } else {
              results += `❌ ${endpoint.name} (${response.status})<br>`;
            }
          } catch (error) {
            results += `❌ ${endpoint.name} (خطأ في الاتصال)<br>`;
          }
        }

        if (successful === endpoints.length) {
          result.className = "test-result success";
          result.innerHTML = `🎉 جميع نقاط API تعمل! (${successful}/${endpoints.length})<br>${results}`;
        } else {
          result.className = "test-result error";
          result.innerHTML = `⚠️ بعض نقاط API لا تعمل (${successful}/${endpoints.length})<br>${results}`;
        }
      }

      // Load Dashboard Preview
      function loadDashboardPreview() {
        const preview = document.getElementById("dashboardPreview");
        const frame = document.getElementById("dashboardFrame");

        if (preview.style.display === "none") {
          frame.src = "index.html";
          preview.style.display = "block";
          preview.scrollIntoView({ behavior: "smooth" });
        } else {
          preview.style.display = "none";
          frame.src = "";
        }
      }
    </script>
  </body>
</html>
