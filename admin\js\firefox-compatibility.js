/**
 * Firefox Compatibility Fixes
 * Handles browser-specific issues for Firefox
 */

class FirefoxCompatibility {
    constructor() {
        this.isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        this.init();
    }

    init() {
        if (this.isFirefox) {
            console.log('🦊 Firefox detected - applying compatibility fixes');
            this.fixFetchRequests();
            this.fixJSONParsing();
            this.fixCORSHeaders();
        }
    }

    /**
     * Fix fetch requests for Firefox
     */
    fixFetchRequests() {
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options = {}) {
            // Add Firefox-specific headers
            const firefoxOptions = {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache',
                    ...options.headers
                },
                mode: 'cors',
                credentials: 'same-origin'
            };

            return originalFetch(url, firefoxOptions)
                .then(response => {
                    // Check if response is ok
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    // Check content type
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        console.warn('⚠️ Response is not JSON:', contentType);
                        return response.text().then(text => {
                            try {
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('❌ Failed to parse response as JSON:', text);
                                throw new Error('Invalid JSON response');
                            }
                        });
                    }
                    
                    return response.json();
                })
                .catch(error => {
                    console.error('🦊 Firefox fetch error:', error);
                    throw error;
                });
        };
    }

    /**
     * Fix JSON parsing issues
     */
    fixJSONParsing() {
        const originalParse = JSON.parse;
        
        JSON.parse = function(text, reviver) {
            try {
                // Clean the text first
                const cleanText = text.trim();
                
                // Check if it's actually JSON
                if (!cleanText.startsWith('{') && !cleanText.startsWith('[')) {
                    console.warn('⚠️ Text does not appear to be JSON:', cleanText.substring(0, 100));
                    throw new Error('Invalid JSON format');
                }
                
                return originalParse(cleanText, reviver);
            } catch (error) {
                console.error('🦊 Firefox JSON parse error:', error);
                console.error('📄 Problematic text:', text.substring(0, 200));
                throw error;
            }
        };
    }

    /**
     * Fix CORS headers for Firefox
     */
    fixCORSHeaders() {
        // Add event listener for CORS errors
        window.addEventListener('error', (event) => {
            if (event.message && event.message.includes('CORS')) {
                console.error('🦊 Firefox CORS error detected:', event);
                this.showCORSError();
            }
        });
    }

    /**
     * Show CORS error message
     */
    showCORSError() {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-warning';
        errorDiv.innerHTML = `
            <h5>🦊 Firefox Compatibility Issue</h5>
            <p>Firefox has detected a CORS (Cross-Origin Resource Sharing) issue.</p>
            <p>This may affect some API calls. Please try refreshing the page.</p>
        `;
        
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(errorDiv, container.firstChild);
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 10000);
    }

    /**
     * Enhanced API call function for Firefox
     */
    static async apiCall(url, options = {}) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                mode: 'cors',
                credentials: 'same-origin',
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const text = await response.text();
            
            try {
                return JSON.parse(text);
            } catch (parseError) {
                console.error('🦊 JSON parse failed:', parseError);
                console.error('📄 Response text:', text);
                throw new Error('Invalid JSON response from server');
            }
        } catch (error) {
            console.error('🦊 Firefox API call failed:', error);
            throw error;
        }
    }
}

// Initialize Firefox compatibility
if (typeof window !== 'undefined') {
    window.firefoxCompat = new FirefoxCompatibility();
    
    // Export for use in other scripts
    window.FirefoxCompatibility = FirefoxCompatibility;
}
