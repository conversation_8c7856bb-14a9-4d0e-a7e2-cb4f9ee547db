/**
 * Remove Diagnostic Popups Script
 * Removes all diagnostic popup windows and prevents them from appearing
 */

console.log('🧹 Remove Diagnostic Popups Script loading...');

// Function to remove all diagnostic popups
function removeAllDiagnosticPopups() {
    console.log('🧹 Removing all diagnostic popups...');
    
    const popupIds = [
        'navigationDiagnosticsReport',
        'comprehensiveFixResults', 
        'specificFixResults',
        'ultimateFixResults',
        'quickFixResults',
        'apiFixResults',
        'navigationFixResults',
        'emergencyFixResults',
        'finalFixResults'
    ];
    
    let removedCount = 0;
    
    popupIds.forEach(id => {
        const popup = document.getElementById(id);
        if (popup) {
            popup.remove();
            removedCount++;
            console.log(`✅ Removed popup: ${id}`);
        }
    });
    
    // Also remove any elements with diagnostic-related classes
    const diagnosticSelectors = [
        '[id*="diagnostic"]',
        '[id*="fix-results"]', 
        '[id*="FixResults"]',
        '[class*="diagnostic"]',
        '[class*="fix-popup"]',
        '[style*="z-index: 1000"]'
    ];
    
    diagnosticSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            // Only remove if it looks like a diagnostic popup
            if (element.style.position === 'fixed' && 
                (element.style.zIndex > 1000 || element.id.includes('Fix') || element.id.includes('diagnostic'))) {
                element.remove();
                removedCount++;
                console.log(`✅ Removed diagnostic element: ${element.id || element.className}`);
            }
        });
    });
    
    console.log(`🧹 Removed ${removedCount} diagnostic popups/elements`);
    
    return removedCount;
}

// Function to prevent future popups
function preventDiagnosticPopups() {
    console.log('🛡️ Setting up popup prevention...');
    
    // Override the display functions to prevent them from showing
    const functionsToDisable = [
        'displaySpecificResults',
        'displayFixResults', 
        'displayUltimateResults',
        'createDiagnosticsReport',
        'displayComprehensiveResults',
        'showQuickFixResults'
    ];
    
    functionsToDisable.forEach(funcName => {
        if (window[funcName]) {
            const originalFunc = window[funcName];
            window[funcName] = function(...args) {
                console.log(`🛡️ Blocked ${funcName} from displaying popup`);
                // Still run the function but don't display results
                if (typeof originalFunc === 'function') {
                    const result = originalFunc.apply(this, args);
                    // If it returns results, log them instead of showing popup
                    if (result) {
                        console.log(`📊 ${funcName} results:`, result);
                    }
                    return result;
                }
            };
            console.log(`✅ Disabled popup function: ${funcName}`);
        }
    });
}

// Function to clean up on interval
function startCleanupInterval() {
    console.log('⏰ Starting cleanup interval...');
    
    setInterval(() => {
        const removed = removeAllDiagnosticPopups();
        if (removed > 0) {
            console.log(`🧹 Interval cleanup removed ${removed} popups`);
        }
    }, 5000); // Check every 5 seconds
}

// Main initialization function
function initPopupRemoval() {
    console.log('🚀 Initializing popup removal system...');
    
    // Remove existing popups
    removeAllDiagnosticPopups();
    
    // Prevent future popups
    preventDiagnosticPopups();
    
    // Start cleanup interval
    startCleanupInterval();
    
    console.log('✅ Popup removal system initialized');
}

// Initialize immediately
initPopupRemoval();

// Also initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPopupRemoval);
} else {
    initPopupRemoval();
}

// Make functions globally available
window.removeAllDiagnosticPopups = removeAllDiagnosticPopups;
window.preventDiagnosticPopups = preventDiagnosticPopups;
window.initPopupRemoval = initPopupRemoval;

console.log('✅ Remove Diagnostic Popups Script loaded');
