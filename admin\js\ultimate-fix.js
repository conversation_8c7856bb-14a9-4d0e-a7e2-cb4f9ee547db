/**
 * Ultimate Fix Script
 * Final solution for all remaining issues
 */

console.log('🚀 Ultimate Fix Script loading...');

// Ultimate fix function
async function runUltimateFix() {
    console.log('🚀 Running ultimate fix...');
    
    const results = {
        timestamp: new Date().toISOString(),
        fixes: [],
        errors: [],
        warnings: []
    };
    
    try {
        // Fix 1: Populate roles table
        console.log('1️⃣ Populating roles table...');
        await fixRolesData(results);
        
        // Fix 2: Fix navigation structure
        console.log('2️⃣ Fixing navigation structure...');
        await fixNavigationStructure(results);
        
        // Fix 3: Fix JavaScript errors
        console.log('3️⃣ Fixing JavaScript errors...');
        await fixJavaScriptIssues(results);
        
        // Fix 4: Test all APIs
        console.log('4️⃣ Testing APIs...');
        await testAllAPIs(results);
        
        // Display results
        displayUltimateResults(results);
        
        console.log('🚀 Ultimate fix completed:', results);
        
    } catch (error) {
        console.error('🚀 Ultimate fix failed:', error);
        results.errors.push(`Ultimate fix failed: ${error.message}`);
        displayUltimateResults(results);
    }
    
    return results;
}

// Fix roles data
async function fixRolesData(results) {
    try {
        // First fix the table structure
        const structureResponse = await fetch('../php/api/fix-roles-table.php');
        if (structureResponse.ok) {
            const structureData = await structureResponse.json();
            if (structureData.success) {
                results.fixes.push('Roles table structure fixed');
            }
        }
        
        // Then populate with data
        const populateResponse = await fetch('../php/api/populate-roles.php');
        if (populateResponse.ok) {
            const populateData = await populateResponse.json();
            if (populateData.success) {
                results.fixes.push(`Roles populated: ${populateData.statistics?.inserted || 0} inserted, ${populateData.statistics?.total || 0} total`);
            } else {
                results.errors.push(`Roles population failed: ${populateData.message}`);
            }
        } else {
            results.errors.push('Roles population request failed');
        }
        
    } catch (error) {
        results.errors.push(`Roles fix error: ${error.message}`);
    }
}

// Fix navigation structure
async function fixNavigationStructure(results) {
    try {
        // Find problematic navigation items
        const navItems = document.querySelectorAll('.admin-nav ul li');
        let fixedItems = 0;
        
        navItems.forEach(item => {
            const text = item.textContent.trim();
            
            // Skip items that are too long (concatenated text)
            if (text.length > 50 && !item.getAttribute('data-section')) {
                item.style.display = 'none';
                fixedItems++;
            }
            
            // Ensure all valid items have proper attributes
            if (item.getAttribute('data-section') && !item.hasAttribute('data-ultimate-fixed')) {
                item.style.cursor = 'pointer';
                item.style.pointerEvents = 'auto';
                item.setAttribute('data-ultimate-fixed', 'true');
                
                // Add click handler
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const sectionId = this.getAttribute('data-section');
                    if (sectionId) {
                        ultimateShowSection(sectionId);
                    }
                });
                
                fixedItems++;
            }
        });
        
        if (fixedItems > 0) {
            results.fixes.push(`Fixed ${fixedItems} navigation items`);
        }
        
    } catch (error) {
        results.errors.push(`Navigation fix error: ${error.message}`);
    }
}

// Fix JavaScript issues
async function fixJavaScriptIssues(results) {
    try {
        // Create missing global objects
        if (typeof window.config === 'undefined') {
            window.config = {
                apiBaseUrl: '../php/api/',
                currentUserId: 1,
                currentUserRole: 'admin'
            };
            results.fixes.push('Created config object');
        }
        
        if (typeof window.notificationManager === 'undefined') {
            window.notificationManager = {
                showSuccess: (msg) => {
                    console.log('✅', msg);
                    showToast(msg, 'success');
                },
                showError: (msg) => {
                    console.error('❌', msg);
                    showToast(msg, 'error');
                },
                showWarning: (msg) => {
                    console.warn('⚠️', msg);
                    showToast(msg, 'warning');
                },
                showInfo: (msg) => {
                    console.info('ℹ️', msg);
                    showToast(msg, 'info');
                }
            };
            results.fixes.push('Created notification manager');
        }
        
        // Create missing functions
        const missingFunctions = [];
        
        if (typeof window.loadDashboard !== 'function') {
            window.loadDashboard = function() {
                console.log('📊 Loading dashboard...');
                ultimateLoadDashboard();
            };
            missingFunctions.push('loadDashboard');
        }
        
        if (typeof window.loadProducts !== 'function') {
            window.loadProducts = function() {
                console.log('📦 Loading products...');
                ultimateLoadProducts();
            };
            missingFunctions.push('loadProducts');
        }
        
        if (typeof window.loadOrders !== 'function') {
            window.loadOrders = function() {
                console.log('🛒 Loading orders...');
                ultimateLoadOrders();
            };
            missingFunctions.push('loadOrders');
        }
        
        if (typeof window.loadReportsAndStatistics !== 'function') {
            window.loadReportsAndStatistics = function() {
                console.log('📊 Loading reports...');
                ultimateLoadReports();
            };
            missingFunctions.push('loadReportsAndStatistics');
        }
        
        if (missingFunctions.length > 0) {
            results.fixes.push(`Created missing functions: ${missingFunctions.join(', ')}`);
        }
        
    } catch (error) {
        results.errors.push(`JavaScript fix error: ${error.message}`);
    }
}

// Test all APIs
async function testAllAPIs(results) {
    try {
        const apis = [
            { name: 'Database Connection', url: '../php/api/test-database-connection.php' },
            { name: 'Roles API', url: '../php/api/test-roles-api.php' },
            { name: 'Dashboard Stats', url: '../php/api/dashboard-stats.php' },
            { name: 'Products API', url: '../php/api/products.php' }
        ];
        
        let passedTests = 0;
        let totalTests = apis.length;
        
        for (const api of apis) {
            try {
                const response = await fetch(api.url);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success !== false) {
                        passedTests++;
                    }
                }
            } catch (error) {
                // API test failed
            }
        }
        
        results.fixes.push(`API tests: ${passedTests}/${totalTests} passed`);
        
    } catch (error) {
        results.errors.push(`API test error: ${error.message}`);
    }
}

// Ultimate show section
function ultimateShowSection(sectionId) {
    console.log(`🚀 Ultimate showing section: ${sectionId}`);
    
    // Hide all sections
    const allSections = document.querySelectorAll('.content-section');
    allSections.forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active');
        
        // Load content
        switch (sectionId) {
            case 'dashboard':
                ultimateLoadDashboard();
                break;
            case 'books':
                ultimateLoadProducts();
                break;
            case 'orders':
                ultimateLoadOrders();
                break;
            case 'landingPages':
                ultimateLoadLandingPages();
                break;
            case 'reports':
                ultimateLoadReports();
                break;
        }
        
        // Update navigation
        document.querySelectorAll('.admin-nav ul li').forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-section') === sectionId) {
                item.classList.add('active');
            }
        });
    }
}

// Ultimate content loaders
function ultimateLoadDashboard() {
    const container = document.getElementById('dashboardContent') || document.getElementById('dashboard');
    if (container) {
        container.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-box" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>المنتجات</h3>
                    <p style="font-size: 1.5rem; margin: 0;" id="totalBooks">--</p>
                </div>
                <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>الطلبات</h3>
                    <p style="font-size: 1.5rem; margin: 0;" id="newOrders">--</p>
                </div>
                <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>المبيعات</h3>
                    <p style="font-size: 1.5rem; margin: 0;" id="totalSales">--</p>
                </div>
            </div>
            <div style="text-align: center; padding: 20px;">
                <h3>مرحباً بك في لوحة الإدارة</h3>
                <p>النظام جاهز للاستخدام</p>
            </div>
        `;
        
        // Try to load real data
        if (typeof loadDashboard === 'function') {
            loadDashboard();
        }
    }
}

function ultimateLoadProducts() {
    const container = document.getElementById('booksContent') || document.getElementById('books');
    if (container) {
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>قائمة المنتجات</h3>
                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
            </div>
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <p style="text-align: center;">جاري تحميل المنتجات...</p>
            </div>
        `;
    }
}

function ultimateLoadOrders() {
    const container = document.getElementById('ordersContent') || document.getElementById('orders');
    if (container) {
        container.innerHTML = `
            <h3 style="margin-bottom: 20px;">قائمة الطلبات</h3>
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <p style="text-align: center;">جاري تحميل الطلبات...</p>
            </div>
        `;
    }
}

function ultimateLoadLandingPages() {
    const container = document.getElementById('landingPagesContent') || document.getElementById('landingPages');
    if (container) {
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>صفحات الهبوط</h3>
                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة صفحة
                </button>
            </div>
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <p style="text-align: center;">جاري تحميل صفحات الهبوط...</p>
            </div>
        `;
    }
}

function ultimateLoadReports() {
    const container = document.getElementById('reportsContent') || document.getElementById('reports');
    if (container) {
        container.innerHTML = `
            <h3 style="margin-bottom: 20px;">التقارير والإحصائيات</h3>
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <p style="text-align: center;">جاري تحميل التقارير...</p>
            </div>
        `;
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#dc3545' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 10000;
        max-width: 300px;
        font-size: 14px;
        animation: slideIn 0.3s ease;
    `;
    
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Display ultimate results
function displayUltimateResults(results) {
    // Remove existing results
    const existingResults = document.getElementById('ultimateFixResults');
    if (existingResults) {
        existingResults.remove();
    }
    
    // Create results popup
    const popup = document.createElement('div');
    popup.id = 'ultimateFixResults';
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #10b981;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        z-index: 10003;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        direction: ltr;
        text-align: left;
    `;
    
    popup.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #10b981;">🚀 Ultimate Fix Results</h3>
            <button onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">✕</button>
        </div>
        
        <div style="margin-bottom: 15px;">
            <strong>Completed:</strong> ${new Date(results.timestamp).toLocaleString()}
        </div>
        
        ${results.fixes.length > 0 ? `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #10b981;">✅ Fixes Applied (${results.fixes.length}):</h4>
                <ul style="font-size: 12px; max-height: 150px; overflow-y: auto;">
                    ${results.fixes.map(fix => `<li style="color: #10b981;">${fix}</li>`).join('')}
                </ul>
            </div>
        ` : ''}
        
        ${results.warnings.length > 0 ? `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #f59e0b;">⚠️ Warnings (${results.warnings.length}):</h4>
                <ul style="font-size: 12px; max-height: 100px; overflow-y: auto;">
                    ${results.warnings.map(warning => `<li style="color: #f59e0b;">${warning}</li>`).join('')}
                </ul>
            </div>
        ` : ''}
        
        ${results.errors.length > 0 ? `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #dc3545;">❌ Errors (${results.errors.length}):</h4>
                <ul style="font-size: 12px; max-height: 100px; overflow-y: auto;">
                    ${results.errors.map(error => `<li style="color: #dc3545;">${error}</li>`).join('')}
                </ul>
            </div>
        ` : ''}
        
        <div style="display: flex; gap: 10px; margin-top: 15px;">
            <button onclick="runUltimateFix()" style="background: #10b981; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔄 Run Again
            </button>
            <button onclick="location.reload()" style="background: #3b82f6; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔄 Reload Page
            </button>
        </div>
    `;
    
    document.body.appendChild(popup);
}

// Auto-run ultimate fix when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Ultimate Fix - DOM Ready');
    
    // Run ultimate fix after other scripts load
    setTimeout(() => {
        runUltimateFix();
    }, 5000);
});

// Make functions globally available
window.runUltimateFix = runUltimateFix;
window.ultimateShowSection = ultimateShowSection;

console.log('✅ Ultimate Fix Script loaded');
