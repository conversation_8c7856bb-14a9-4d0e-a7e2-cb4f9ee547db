<?php
try {
    $pdo = new PDO('mysql:host=localhost;port=3307;dbname=mossab-landing-page;charset=utf8mb4', 'root', '');
    $stmt = $pdo->query('SELECT 1 as test, NOW() as `current_time`');
    $result = $stmt->fetch();

    // Write result to file for debugging
    file_put_contents('query-result.txt', print_r($result, true));
    echo "Query executed successfully. Result saved to query-result.txt";
} catch (PDOException $e) {
    file_put_contents('query-error.txt', $e->getMessage());
    echo 'Error: ' . $e->getMessage();
}
