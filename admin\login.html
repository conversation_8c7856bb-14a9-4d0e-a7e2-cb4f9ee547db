<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تسجيل الدخول - لوحة التحكم - Firebase</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Noto Sans Arabic", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .auth-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 400px;
      }

      .auth-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .auth-header h1 {
        font-size: 1.8rem;
        margin-bottom: 5px;
      }

      .auth-header p {
        opacity: 0.9;
        font-size: 0.9rem;
      }

      .auth-content {
        padding: 30px;
      }

      .auth-tabs {
        display: flex;
        margin-bottom: 30px;
        border-radius: 10px;
        background: #f8f9fa;
        padding: 5px;
      }

      .auth-tab {
        flex: 1;
        padding: 10px;
        text-align: center;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
      }

      .auth-tab.active {
        background: #667eea;
        color: white;
      }

      .auth-form {
        display: none;
      }

      .auth-form.active {
        display: block;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }

      .form-group input {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 16px;
        transition: border-color 0.3s ease;
        font-family: inherit;
      }

      .form-group input:focus {
        outline: none;
        border-color: #667eea;
      }

      .auth-button {
        width: 100%;
        padding: 12px;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 15px;
        font-family: inherit;
      }

      .auth-button.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .auth-button.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .auth-button.google {
        background: white;
        color: #333;
        border: 2px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .auth-button.google:hover {
        border-color: #667eea;
        transform: translateY(-2px);
      }

      .auth-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        font-size: 14px;
        display: none;
      }

      .success-message {
        background: #d4edda;
        color: #155724;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        font-size: 14px;
        display: none;
      }

      .divider {
        text-align: center;
        margin: 20px 0;
        position: relative;
        color: #6c757d;
      }

      .divider::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e9ecef;
      }

      .divider span {
        background: white;
        padding: 0 15px;
      }

      .back-link {
        text-align: center;
        margin-top: 20px;
      }

      .back-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
      }

      .back-link a:hover {
        text-decoration: underline;
      }

      .demo-accounts {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .demo-accounts h3 {
        margin: 0 0 10px 0;
        color: #495057;
        font-size: 14px;
      }

      .demo-account {
        margin: 5px 0;
        cursor: pointer;
        font-size: 12px;
        color: #6c757d;
        padding: 5px;
        border-radius: 4px;
        transition: background-color 0.3s ease;
      }

      .demo-account:hover {
        background-color: #e9ecef;
      }

      .demo-account strong {
        color: #495057;
      }
    </style>
  </head>
  <body>
    <div class="auth-container">
      <div class="auth-header">
        <h1>🔐 تسجيل الدخول</h1>
        <p>لوحة التحكم - Firebase Auth</p>
      </div>

      <div class="auth-content">
        <!-- Auth Tabs -->
        <div class="auth-tabs">
          <div class="auth-tab active" data-tab="signin">تسجيل الدخول</div>
          <div class="auth-tab" data-tab="signup">إنشاء حساب</div>
        </div>

        <!-- Messages -->
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <!-- Sign In Form -->
        <form class="auth-form active" id="signinForm">
          <div class="form-group">
            <label for="signinEmail">البريد الإلكتروني</label>
            <input type="email" id="signinEmail" required />
          </div>
          <div class="form-group">
            <label for="signinPassword">كلمة المرور</label>
            <input type="password" id="signinPassword" required />
          </div>
          <button type="submit" class="auth-button primary">
            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
          </button>
        </form>

        <!-- Sign Up Form -->
        <form class="auth-form" id="signupForm">
          <div class="form-group">
            <label for="signupName">الاسم الكامل</label>
            <input type="text" id="signupName" required />
          </div>
          <div class="form-group">
            <label for="signupEmail">البريد الإلكتروني</label>
            <input type="email" id="signupEmail" required />
          </div>
          <div class="form-group">
            <label for="signupPassword">كلمة المرور</label>
            <input type="password" id="signupPassword" required minlength="6" />
          </div>
          <button type="submit" class="auth-button primary">
            <i class="fas fa-user-plus"></i> إنشاء حساب
          </button>
        </form>

        <!-- Divider -->
        <div class="divider">
          <span>أو</span>
        </div>

        <!-- Google Sign In -->
        <button class="auth-button google" id="googleSignInBtn">
          <i class="fab fa-google"></i>
          تسجيل الدخول بـ Google
        </button>

        <!-- Demo Accounts Info -->
        <div class="demo-accounts">
          <h3>حسابات تجريبية:</h3>
          <div
            class="demo-account"
            onclick="fillCredentials('<EMAIL>', 'Admin123!@#')"
          >
            <strong><EMAIL></strong> / Admin123!@# (مدير رئيسي)
          </div>
          <div
            class="demo-account"
            onclick="fillCredentials('<EMAIL>', 'Mossaab2024!')"
          >
            <strong><EMAIL></strong> / Mossaab2024! (مالك المتجر)
          </div>
          <div
            class="demo-account"
            onclick="fillCredentials('<EMAIL>', 'Manager123!')"
          >
            <strong><EMAIL></strong> / Manager123! (مدير)
          </div>
          <div
            class="demo-account"
            onclick="fillCredentials('<EMAIL>', 'Demo123!')"
          >
            <strong><EMAIL></strong> / Demo123! (تجريبي)
          </div>
          <p style="font-size: 11px; color: #6c757d; margin-top: 10px">
            انقر على أي حساب لملء البيانات تلقائياً
          </p>
        </div>

        <!-- Back Link -->
        <div class="back-link">
          <a href="../index.html">العودة إلى الموقع</a> |
          <a href="setup-firebase-admins.html">إعداد المديرين</a>
        </div>
      </div>
    </div>

    <!-- Auth Fix Script (load first) -->
    <script src="auth-fix.js"></script>

    <!-- Firebase and App Scripts -->
    <script type="module" src="js/firebase-config.js"></script>

    <script type="module">
      // Function to fill credentials automatically
      window.fillCredentials = function (email, password) {
        document.getElementById("signinEmail").value = email;
        document.getElementById("signinPassword").value = password;
        // Switch to sign in tab
        document
          .querySelectorAll(".auth-tab")
          .forEach((t) => t.classList.remove("active"));
        document.querySelector('[data-tab="signin"]').classList.add("active");
        document
          .querySelectorAll(".auth-form")
          .forEach((f) => f.classList.remove("active"));
        document.getElementById("signinForm").classList.add("active");
      };

      // Tab switching
      document.querySelectorAll(".auth-tab").forEach((tab) => {
        tab.addEventListener("click", () => {
          const tabName = tab.dataset.tab;

          // Update active tab
          document
            .querySelectorAll(".auth-tab")
            .forEach((t) => t.classList.remove("active"));
          tab.classList.add("active");

          // Update active form
          document
            .querySelectorAll(".auth-form")
            .forEach((f) => f.classList.remove("active"));
          document.getElementById(tabName + "Form").classList.add("active");

          // Clear messages
          clearMessages();
        });
      });

      // Sign In Form
      document
        .getElementById("signinForm")
        .addEventListener("submit", async (e) => {
          e.preventDefault();

          const email = document.getElementById("signinEmail").value;
          const password = document.getElementById("signinPassword").value;

          setLoading(true);
          clearMessages();

          const result = await window.firebaseAuth.signInWithEmail(
            email,
            password
          );

          if (result.success) {
            showSuccess("تم تسجيل الدخول بنجاح! جاري التوجيه...");
            setTimeout(() => {
              window.safeRedirect("index.html");
            }, 1500);
          } else {
            showError(result.error);
          }

          setLoading(false);
        });

      // Sign Up Form
      document
        .getElementById("signupForm")
        .addEventListener("submit", async (e) => {
          e.preventDefault();

          const name = document.getElementById("signupName").value;
          const email = document.getElementById("signupEmail").value;
          const password = document.getElementById("signupPassword").value;

          setLoading(true);
          clearMessages();

          // Create admin account with admin role
          const result = await window.firebaseAuth.signUpWithEmail(
            email,
            password,
            name,
            "admin"
          );

          if (result.success) {
            showSuccess("تم إنشاء الحساب بنجاح! جاري التوجيه...");
            setTimeout(() => {
              window.safeRedirect("index.html");
            }, 1500);
          } else {
            showError(result.error);
          }

          setLoading(false);
        });

      // Google Sign In
      document
        .getElementById("googleSignInBtn")
        .addEventListener("click", async () => {
          setLoading(true);
          clearMessages();

          const result = await window.firebaseAuth.signInWithGoogle();

          if (result.success) {
            showSuccess("تم تسجيل الدخول بـ Google بنجاح! جاري التوجيه...");
            setTimeout(() => {
              window.safeRedirect("index.html");
            }, 1500);
          } else {
            showError(result.error);
          }

          setLoading(false);
        });

      // Utility functions
      function setLoading(loading) {
        const buttons = document.querySelectorAll(".auth-button");
        buttons.forEach((btn) => (btn.disabled = loading));

        if (loading) {
          buttons.forEach((btn) => {
            if (btn.id === "googleSignInBtn") {
              btn.innerHTML =
                '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
            } else if (btn.closest("#signinForm")) {
              btn.innerHTML =
                '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            } else if (btn.closest("#signupForm")) {
              btn.innerHTML =
                '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
            }
          });
        } else {
          document.querySelector("#signinForm .auth-button").innerHTML =
            '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
          document.querySelector("#signupForm .auth-button").innerHTML =
            '<i class="fas fa-user-plus"></i> إنشاء حساب';
          document.getElementById("googleSignInBtn").innerHTML =
            '<i class="fab fa-google"></i> تسجيل الدخول بـ Google';
        }
      }

      function showError(message) {
        const errorDiv = document.getElementById("errorMessage");
        errorDiv.textContent = message;
        errorDiv.style.display = "block";
      }

      function showSuccess(message) {
        const successDiv = document.getElementById("successMessage");
        successDiv.textContent = message;
        successDiv.style.display = "block";
      }

      function clearMessages() {
        document.getElementById("errorMessage").style.display = "none";
        document.getElementById("successMessage").style.display = "none";
      }

      // Check if user is already signed in
      window.onFirebaseUserSignedIn = (user, profile) => {
        console.log("User already signed in, redirecting...");
        // Only redirect if user has admin privileges
        if (
          profile &&
          ["admin", "super_admin", "owner", "manager"].includes(profile.role)
        ) {
          window.safeRedirect("index.html");
        } else {
          showError("ليس لديك صلاحيات للوصول إلى لوحة التحكم");
          window.firebaseAuth.signOutUser();
        }
      };

      // Wait for Firebase to initialize before checking auth
      setTimeout(() => {
        if (window.firebaseAuth?.isAuthenticated()) {
          const userInfo = window.firebaseAuth.getCurrentUser();
          if (userInfo.isAdmin) {
            console.log("Admin already authenticated, redirecting...");
            window.safeRedirect("index.html");
          }
        }
      }, 2000);
    </script>
  </body>
</html>
