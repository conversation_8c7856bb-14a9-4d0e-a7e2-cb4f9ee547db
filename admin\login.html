<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="css/login.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="login-container">
      <div class="login-box">
        <div class="login-header">
          <h1>تسجيل الدخول</h1>
          <p>لوحة تحكم متجر الكتب</p>
        </div>

        <form id="loginForm" class="login-form">
          <div class="form-group">
            <label for="username">اسم المستخدم</label>
            <input type="text" id="username" name="username" required />
          </div>

          <div class="form-group">
            <label for="password">كلمة المرور</label>
            <input type="password" id="password" name="password" required />
          </div>

          <div class="error-message" id="errorMessage"></div>

          <button type="submit" class="login-button">تسجيل الدخول</button>
        </form>

        <!-- Demo Accounts Info -->
        <div
          class="demo-accounts"
          style="
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
          "
        >
          <h3 style="margin: 0 0 10px 0; color: #495057; font-size: 14px">
            حسابات تجريبية:
          </h3>
          <div style="font-size: 12px; color: #6c757d">
            <div style="margin: 5px 0">
              <strong>admin</strong> / admin123 (مدير رئيسي)
            </div>
            <div style="margin: 5px 0">
              <strong>mossaab</strong> / mossaab2024 (مالك المتجر)
            </div>
            <div style="margin: 5px 0">
              <strong>manager</strong> / manager123 (مدير)
            </div>
            <div style="margin: 5px 0">
              <strong>demo</strong> / demo123 (تجريبي)
            </div>
          </div>
        </div>

        <div class="back-to-site">
          <a href="../index.html">العودة إلى الموقع</a>
        </div>
      </div>
    </div>

    <script>
      document
        .getElementById("loginForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const username = document.getElementById("username").value;
          const password = document.getElementById("password").value;

          fetch("../php/admin.php?action=login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              username: username,
              password: password,
            }),
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                window.location.href = "index.html";
              } else {
                document.getElementById("errorMessage").textContent =
                  data.error || "خطأ في تسجيل الدخول";
              }
            })
            .catch((error) => {
              document.getElementById("errorMessage").textContent =
                "حدث خطأ في الاتصال";
            });
        });
    </script>
  </body>
</html>
