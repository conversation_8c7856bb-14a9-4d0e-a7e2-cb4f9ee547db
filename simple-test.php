<?php
header('Content-Type: application/json; charset=utf-8');

try {
    // Test basic PHP functionality
    echo json_encode([
        'success' => true,
        'message' => 'PHP is working',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>