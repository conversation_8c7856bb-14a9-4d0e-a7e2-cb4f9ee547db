/**
 * Quick Fix Script
 * Immediate fixes for current issues
 */

console.log('⚡ Quick Fix Script loading...');

// Quick fix function
function applyQuickFixes() {
    console.log('⚡ Applying quick fixes...');

    const fixes = [];
    const errors = [];

    try {
        // Fix 1: Navigation issues
        fixNavigationIssues(fixes, errors);

        // Fix 2: JavaScript errors
        fixJavaScriptErrors(fixes, errors);

        // Fix 3: Missing functions
        createMissingFunctions(fixes, errors);

        // Fix 4: UI issues
        fixUIIssues(fixes, errors);

        // Display results
        console.log('⚡ Quick fixes applied:', fixes);
        if (errors.length > 0) {
            console.error('⚡ Quick fix errors:', errors);
        }

        // Show notification
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showSuccess(`تم تطبيق ${fixes.length} إصلاح سريع`);
        }

        return { fixes, errors };

    } catch (error) {
        console.error('⚡ Quick fix failed:', error);
        return { fixes, errors: [...errors, error.message] };
    }
}

// Fix navigation issues
function fixNavigationIssues(fixes, errors) {
    try {
        // Make all navigation items clickable
        const navItems = document.querySelectorAll('.admin-nav ul li[data-section]');
        let fixedItems = 0;

        navItems.forEach(item => {
            if (!item.hasAttribute('data-quick-fixed')) {
                // Make clickable
                item.style.cursor = 'pointer';
                item.style.pointerEvents = 'auto';
                item.style.userSelect = 'none';

                // Add click handler
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const sectionId = this.getAttribute('data-section');
                    if (sectionId) {
                        quickShowSection(sectionId);

                        // Update active state
                        document.querySelectorAll('.admin-nav ul li').forEach(li => {
                            li.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });

                // Mark as fixed
                item.setAttribute('data-quick-fixed', 'true');
                fixedItems++;
            }
        });

        if (fixedItems > 0) {
            fixes.push(`Fixed ${fixedItems} navigation items`);
        }

    } catch (error) {
        errors.push(`Navigation fix error: ${error.message}`);
    }
}

// Fix JavaScript errors
function fixJavaScriptErrors(fixes, errors) {
    try {
        // Fix undefined config
        if (typeof config === 'undefined') {
            window.config = {
                apiBaseUrl: '../php/api/',
                currentUserId: 1,
                currentUserRole: 'admin'
            };
            fixes.push('Created missing config object');
        }

        // Fix undefined notificationManager
        if (typeof notificationManager === 'undefined') {
            window.notificationManager = {
                showSuccess: (msg) => console.log('✅', msg),
                showError: (msg) => console.error('❌', msg),
                showWarning: (msg) => console.warn('⚠️', msg),
                showInfo: (msg) => console.info('ℹ️', msg)
            };
            fixes.push('Created fallback notification manager');
        }

    } catch (error) {
        errors.push(`JavaScript fix error: ${error.message}`);
    }
}

// Create missing functions
function createMissingFunctions(fixes, errors) {
    try {
        const missingFunctions = [];

        // Dashboard function
        if (typeof loadDashboard !== 'function') {
            window.loadDashboard = function() {
                console.log('📊 Loading dashboard...');
                const container = document.getElementById('dashboardContent') || document.getElementById('dashboard');
                if (container) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <h3>لوحة المعلومات</h3>
                            <p>مرحباً بك في لوحة الإدارة</p>
                        </div>
                    `;
                }
            };
            missingFunctions.push('loadDashboard');
        }

        // Products function
        if (typeof loadProducts !== 'function' && typeof window.loadProducts !== 'function') {
            window.loadProducts = function() {
                console.log('📦 Loading products...');
                const container = document.getElementById('booksContent') || document.getElementById('books');
                if (container) {
                    container.innerHTML = `
                        <div style="padding: 20px;">
                            <h3>إدارة المنتجات</h3>
                            <p>جاري تحميل المنتجات...</p>
                        </div>
                    `;
                }
            };
            missingFunctions.push('loadProducts');
        }

        // Orders function
        if (typeof loadOrders !== 'function') {
            window.loadOrders = function() {
                console.log('🛒 Loading orders...');
                const container = document.getElementById('ordersContent') || document.getElementById('orders');
                if (container) {
                    container.innerHTML = `
                        <div style="padding: 20px;">
                            <h3>الطلبات</h3>
                            <p>جاري تحميل الطلبات...</p>
                        </div>
                    `;
                }
            };
            missingFunctions.push('loadOrders');
        }

        // Landing pages function
        if (typeof loadLandingPages !== 'function') {
            window.loadLandingPages = function() {
                console.log('📄 Loading landing pages...');
                const container = document.getElementById('landingPagesContent') || document.getElementById('landingPages');
                if (container) {
                    container.innerHTML = `
                        <div style="padding: 20px;">
                            <h3>صفحات الهبوط</h3>
                            <p>جاري تحميل صفحات الهبوط...</p>
                        </div>
                    `;
                }
            };
            missingFunctions.push('loadLandingPages');
        }

        // Reports function
        if (typeof loadReportsAndStatistics !== 'function') {
            window.loadReportsAndStatistics = function() {
                console.log('📊 Loading reports...');
                const container = document.getElementById('reportsContent') || document.getElementById('reports');
                if (container) {
                    container.innerHTML = `
                        <div style="padding: 20px;">
                            <h3>التقارير والإحصائيات</h3>
                            <p>جاري تحميل التقارير...</p>
                        </div>
                    `;
                }
            };
            missingFunctions.push('loadReportsAndStatistics');
        }

        if (missingFunctions.length > 0) {
            fixes.push(`Created ${missingFunctions.length} missing functions: ${missingFunctions.join(', ')}`);
        }

    } catch (error) {
        errors.push(`Missing functions fix error: ${error.message}`);
    }
}

// Quick show section
function quickShowSection(sectionId) {
    console.log(`⚡ Quick showing section: ${sectionId}`);

    // Hide all sections
    const allSections = document.querySelectorAll('.content-section');
    allSections.forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active');

        // Load content based on section
        switch (sectionId) {
            case 'dashboard':
                if (typeof loadDashboard === 'function') loadDashboard();
                break;
            case 'books':
                if (typeof loadProducts === 'function') loadProducts();
                else if (typeof window.loadProducts === 'function') window.loadProducts();
                break;
            case 'orders':
                if (typeof loadOrders === 'function') loadOrders();
                break;
            case 'landingPages':
                if (typeof loadLandingPages === 'function') loadLandingPages();
                break;
            case 'reports':
                if (typeof loadReportsAndStatistics === 'function') loadReportsAndStatistics();
                break;
        }

        // Update page title
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            const titles = {
                'dashboard': 'لوحة المعلومات',
                'books': 'إدارة المنتجات',
                'orders': 'الطلبات',
                'landingPages': 'صفحات الهبوط',
                'reports': 'التقارير والإحصائيات'
            };
            pageTitle.textContent = titles[sectionId] || sectionId;
        }
    }
}

// Fix UI issues
function fixUIIssues(fixes, errors) {
    try {
        // Ensure RTL
        document.documentElement.setAttribute('dir', 'rtl');
        document.documentElement.setAttribute('lang', 'ar');

        // Fix sidebar visibility
        const sidebar = document.querySelector('.admin-nav');
        if (sidebar) {
            sidebar.style.display = 'block';
            sidebar.style.visibility = 'visible';
        }

        // Ensure at least one section is visible
        const visibleSections = document.querySelectorAll('.content-section[style*="display: block"], .content-section.active');
        if (visibleSections.length === 0) {
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.style.display = 'block';
                dashboard.classList.add('active');
                fixes.push('Activated dashboard section');
            }
        }

        fixes.push('UI issues checked and fixed');

    } catch (error) {
        errors.push(`UI fix error: ${error.message}`);
    }
}

// Auto-apply quick fixes when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('⚡ Quick Fix - DOM Ready');

    // Apply fixes with delays
    setTimeout(applyQuickFixes, 1000);
    setTimeout(applyQuickFixes, 3000);
});

// Test database function
async function testDatabase() {
    console.log('🔍 Testing database connection...');

    try {
        const response = await fetch('../php/api/test-database-connection.php');
        const data = await response.json();

        // Create test results popup
        const popup = document.createElement('div');
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid ${data.success ? '#10b981' : '#dc3545'};
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10002;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        `;

        popup.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: ${data.success ? '#10b981' : '#dc3545'};">
                    ${data.success ? '✅' : '❌'} Database Test Results
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">✕</button>
            </div>

            <div style="margin-bottom: 15px;">
                <strong>Status:</strong> ${data.success ? 'Connected' : 'Failed'}<br>
                <strong>Message:</strong> ${data.message}<br>
                <strong>Time:</strong> ${data.timestamp}
            </div>

            ${data.connection_info ? `
                <div style="margin-bottom: 15px;">
                    <h4>Connection Info:</h4>
                    <ul>
                        <li>Host: ${data.connection_info.host}</li>
                        <li>Port: ${data.connection_info.port}</li>
                        <li>Database: ${data.connection_info.database}</li>
                    </ul>
                </div>
            ` : ''}

            ${data.tables_count !== undefined ? `
                <div style="margin-bottom: 15px;">
                    <strong>Tables Found:</strong> ${data.tables_count}
                    ${data.sample_tables ? `<br><small>Sample: ${data.sample_tables.join(', ')}</small>` : ''}
                </div>
            ` : ''}

            ${data.details && data.details.length > 0 ? `
                <div style="margin-bottom: 15px;">
                    <h4>Details:</h4>
                    <ul style="font-size: 12px;">
                        ${data.details.map(detail => `<li>${detail}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            ${data.system_info ? `
                <div style="margin-bottom: 15px;">
                    <h4>System Info:</h4>
                    <ul style="font-size: 12px;">
                        <li>PHP: ${data.system_info.php_version}</li>
                        <li>PDO: ${data.system_info.pdo_available ? 'Available' : 'Not Available'}</li>
                        <li>PDO MySQL: ${data.system_info.pdo_mysql_available ? 'Available' : 'Not Available'}</li>
                    </ul>
                </div>
            ` : ''}
        `;

        document.body.appendChild(popup);

        // Show notification
        if (typeof notificationManager !== 'undefined') {
            if (data.success) {
                notificationManager.showSuccess('Database connection successful');
            } else {
                notificationManager.showError('Database connection failed');
            }
        }

    } catch (error) {
        console.error('Database test error:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('Database test failed: ' + error.message);
        }
    }
}

// Make functions globally available
window.applyQuickFixes = applyQuickFixes;
window.quickShowSection = quickShowSection;
window.testDatabase = testDatabase;

console.log('✅ Quick Fix Script loaded');
