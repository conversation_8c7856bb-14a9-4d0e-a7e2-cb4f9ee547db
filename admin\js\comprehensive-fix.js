/**
 * Comprehensive Fix Script
 * Fixes all known issues in the admin panel
 */

console.log('🔧 Comprehensive Fix Script loading...');

// Configuration
const FIX_CONFIG = {
    apiBaseUrl: '../php/api/',
    maxRetries: 3,
    retryDelay: 1000
};

// Main fix function
async function runComprehensiveFix() {
    console.log('🔧 Running comprehensive fix...');

    const results = {
        timestamp: new Date().toISOString(),
        fixes: [],
        errors: [],
        warnings: []
    };

    try {
        // Fix 1: Database schema
        console.log('🔧 Fixing database schema...');
        await fixDatabaseSchema(results);

        // Fix 2: Navigation issues
        console.log('🔧 Fixing navigation...');
        await fixNavigation(results);

        // Fix 3: JavaScript errors
        console.log('🔧 Fixing JavaScript errors...');
        await fixJavaScriptErrors(results);

        // Fix 4: API calls
        console.log('🔧 Fixing API calls...');
        await fixApiCalls(results);

        // Fix 5: UI issues
        console.log('🔧 Fixing UI issues...');
        await fixUIIssues(results);

        // Display results
        displayFixResults(results);

        console.log('✅ Comprehensive fix completed:', results);

    } catch (error) {
        console.error('❌ Comprehensive fix failed:', error);
        results.errors.push(`Comprehensive fix failed: ${error.message}`);
        displayFixResults(results);
    }

    return results;
}

// Fix database schema
async function fixDatabaseSchema(results) {
    try {
        // First test database connection
        console.log('🔍 Testing database connection...');
        const testResponse = await fetch(`${FIX_CONFIG.apiBaseUrl}test-database-connection.php`);

        if (!testResponse.ok) {
            results.errors.push(`Database test failed: HTTP ${testResponse.status}`);
            return;
        }

        const testData = await testResponse.json();

        if (testData.success) {
            results.fixes.push('Database connection test passed');
            console.log('✅ Database connection successful');

            // Now try to fix schema
            console.log('🔧 Attempting schema fixes...');
            const fixResponse = await fetch(`${FIX_CONFIG.apiBaseUrl}fix-database-schema.php`);

            if (fixResponse.ok) {
                const fixData = await fixResponse.json();

                if (fixData.success) {
                    results.fixes.push(...fixData.fixes);
                    if (fixData.errors && fixData.errors.length > 0) {
                        results.errors.push(...fixData.errors);
                    }
                    console.log('✅ Database schema fixes applied');
                } else {
                    results.errors.push(`Schema fix failed: ${fixData.message}`);
                }
            } else {
                results.errors.push(`Schema fix request failed: HTTP ${fixResponse.status}`);
            }

        } else {
            results.errors.push(`Database connection failed: ${testData.message}`);
            results.warnings.push('Skipping schema fixes due to connection issues');
        }

    } catch (error) {
        results.errors.push(`Database fix error: ${error.message}`);
        console.error('❌ Database fix error:', error);
    }
}

// Fix navigation
async function fixNavigation(results) {
    try {
        // Force navigation fix
        if (typeof finalNavigationFix !== 'undefined' && finalNavigationFix.init) {
            finalNavigationFix.init();
            results.fixes.push('Navigation system reinitialized');
        }

        // Check navigation items
        const navItems = document.querySelectorAll('.admin-nav ul li[data-section]');
        let fixedItems = 0;

        navItems.forEach(item => {
            if (!item.hasAttribute('data-nav-fixed')) {
                item.style.cursor = 'pointer';
                item.style.pointerEvents = 'auto';
                item.setAttribute('data-nav-fixed', 'true');
                fixedItems++;
            }
        });

        if (fixedItems > 0) {
            results.fixes.push(`Fixed ${fixedItems} navigation items`);
        }

        console.log('✅ Navigation fixed');

    } catch (error) {
        results.errors.push(`Navigation fix error: ${error.message}`);
        console.error('❌ Navigation fix error:', error);
    }
}

// Fix JavaScript errors
async function fixJavaScriptErrors(results) {
    try {
        // Fix undefined variables
        if (typeof config === 'undefined') {
            window.config = {
                apiBaseUrl: '../php/api/',
                currentUserId: 1,
                currentUserRole: 'admin'
            };
            results.fixes.push('Created missing config object');
        }

        // Fix missing functions
        const missingFunctions = [];

        if (typeof loadDashboard !== 'function') {
            window.loadDashboard = function() {
                console.log('Loading dashboard...');
                const container = document.getElementById('dashboardContent') || document.getElementById('dashboard');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; padding: 20px;">لوحة المعلومات جاهزة</p>';
                }
            };
            missingFunctions.push('loadDashboard');
        }

        if (typeof loadProducts !== 'function' && typeof window.loadProducts !== 'function') {
            window.loadProducts = function() {
                console.log('Loading products...');
                const container = document.getElementById('booksContent') || document.getElementById('books');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; padding: 20px;">جاري تحميل المنتجات...</p>';
                }
            };
            missingFunctions.push('loadProducts');
        }

        if (typeof loadOrders !== 'function') {
            window.loadOrders = function() {
                console.log('Loading orders...');
                const container = document.getElementById('ordersContent') || document.getElementById('orders');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; padding: 20px;">جاري تحميل الطلبات...</p>';
                }
            };
            missingFunctions.push('loadOrders');
        }

        if (typeof loadReportsAndStatistics !== 'function') {
            window.loadReportsAndStatistics = function() {
                console.log('Loading reports...');
                const container = document.getElementById('reportsContent') || document.getElementById('reports');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; padding: 20px;">جاري تحميل التقارير...</p>';
                }
            };
            missingFunctions.push('loadReportsAndStatistics');
        }

        if (missingFunctions.length > 0) {
            results.fixes.push(`Created missing functions: ${missingFunctions.join(', ')}`);
        }

        console.log('✅ JavaScript errors fixed');

    } catch (error) {
        results.errors.push(`JavaScript fix error: ${error.message}`);
        console.error('❌ JavaScript fix error:', error);
    }
}

// Fix API calls
async function fixApiCalls(results) {
    try {
        // Test API endpoints
        const endpoints = [
            'admin-dashboard.php?action=overview',
            'products-multi-user.php',
            'dashboard-stats.php',
            'roles.php'
        ];

        let workingEndpoints = 0;
        let failedEndpoints = 0;

        for (const endpoint of endpoints) {
            try {
                const response = await fetch(`${FIX_CONFIG.apiBaseUrl}${endpoint}`);
                if (response.ok) {
                    workingEndpoints++;
                } else {
                    failedEndpoints++;
                    results.warnings.push(`API endpoint ${endpoint} returned ${response.status}`);
                }
            } catch (error) {
                failedEndpoints++;
                results.warnings.push(`API endpoint ${endpoint} failed: ${error.message}`);
            }
        }

        results.fixes.push(`API test completed: ${workingEndpoints} working, ${failedEndpoints} failed`);
        console.log('✅ API calls tested');

    } catch (error) {
        results.errors.push(`API fix error: ${error.message}`);
        console.error('❌ API fix error:', error);
    }
}

// Fix UI issues
async function fixUIIssues(results) {
    try {
        // Fix RTL layout
        document.documentElement.setAttribute('dir', 'rtl');
        document.documentElement.setAttribute('lang', 'ar');

        // Fix missing elements
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) {
            results.warnings.push('Main content container not found');
        }

        // Fix sidebar visibility
        const sidebar = document.querySelector('.admin-nav');
        if (sidebar) {
            sidebar.style.display = 'block';
            sidebar.style.visibility = 'visible';
        }

        // Fix content sections
        const sections = document.querySelectorAll('.content-section');
        let visibleSections = 0;
        sections.forEach(section => {
            if (window.getComputedStyle(section).display !== 'none') {
                visibleSections++;
            }
        });

        if (visibleSections === 0) {
            // Show dashboard by default
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.style.display = 'block';
                dashboard.classList.add('active');
                results.fixes.push('Activated dashboard section');
            }
        }

        results.fixes.push('UI issues checked and fixed');
        console.log('✅ UI issues fixed');

    } catch (error) {
        results.errors.push(`UI fix error: ${error.message}`);
        console.error('❌ UI fix error:', error);
    }
}

// Display fix results
function displayFixResults(results) {
    // Remove existing results
    const existingResults = document.getElementById('comprehensiveFixResults');
    if (existingResults) {
        existingResults.remove();
    }

    // Create results container
    const resultsContainer = document.createElement('div');
    resultsContainer.id = 'comprehensiveFixResults';
    resultsContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        background: white;
        border: 2px solid #10b981;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        z-index: 10001;
        font-family: Arial, sans-serif;
        font-size: 14px;
        direction: ltr;
        text-align: left;
    `;

    // Create content
    let html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #10b981;">🔧 Comprehensive Fix Results</h3>
            <button onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">✕</button>
        </div>

        <div style="margin-bottom: 15px;">
            <strong>Completed:</strong> ${new Date(results.timestamp).toLocaleString()}
        </div>
    `;

    // Fixes section
    if (results.fixes.length > 0) {
        html += `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #10b981; margin: 0 0 10px 0;">✅ Fixes Applied (${results.fixes.length})</h4>
                <ul style="margin: 0; padding-left: 20px; max-height: 150px; overflow-y: auto;">
                    ${results.fixes.map(fix => `<li style="color: #10b981; margin-bottom: 5px;">${fix}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Warnings section
    if (results.warnings.length > 0) {
        html += `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #f59e0b; margin: 0 0 10px 0;">⚠️ Warnings (${results.warnings.length})</h4>
                <ul style="margin: 0; padding-left: 20px; max-height: 100px; overflow-y: auto;">
                    ${results.warnings.map(warning => `<li style="color: #f59e0b; margin-bottom: 5px;">${warning}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Errors section
    if (results.errors.length > 0) {
        html += `
            <div style="margin-bottom: 15px;">
                <h4 style="color: #dc3545; margin: 0 0 10px 0;">❌ Errors (${results.errors.length})</h4>
                <ul style="margin: 0; padding-left: 20px; max-height: 100px; overflow-y: auto;">
                    ${results.errors.map(error => `<li style="color: #dc3545; margin-bottom: 5px;">${error}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Action buttons
    html += `
        <div style="display: flex; gap: 10px; margin-top: 15px;">
            <button onclick="runComprehensiveFix()" style="background: #10b981; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔄 Run Again
            </button>
            <button onclick="location.reload()" style="background: #3b82f6; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; flex: 1;">
                🔄 Reload Page
            </button>
        </div>
    `;

    resultsContainer.innerHTML = html;
    document.body.appendChild(resultsContainer);
}

// Auto-run fix when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Comprehensive Fix - DOM Ready');

    // Run fix after other scripts load
    setTimeout(() => {
        runComprehensiveFix();
    }, 3000);
});

// Make function globally available
window.runComprehensiveFix = runComprehensiveFix;

console.log('✅ Comprehensive Fix Script loaded');
