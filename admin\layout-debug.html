<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Debug Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
            direction: rtl;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .debug-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: -30px -30px 30px -30px;
            padding: 30px;
            border-radius: 12px 12px 0 0;
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .debug-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .debug-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .debug-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .fix-button {
            background: #10b981;
        }
        .fix-button:hover {
            background: #059669;
        }
        .layout-info {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            border-left: 4px solid #3b82f6;
        }
        .visual-debug {
            position: relative;
            height: 200px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        .sidebar-visual {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .content-visual {
            height: 100%;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #4a5568;
            border-right: 2px dashed #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <h1><i class="fas fa-bug"></i> Layout Debug Tool</h1>
            <p>أداة تشخيص وإصلاح مشاكل التخطيط في لوحة التحكم</p>
        </div>

        <!-- Current Layout Analysis -->
        <div class="debug-section">
            <h3><i class="fas fa-search"></i> تحليل التخطيط الحالي</h3>
            <button class="debug-button" onclick="analyzeCurrentLayout()">
                <i class="fas fa-play"></i> تحليل التخطيط
            </button>
            <div id="layoutAnalysis"></div>
            <div id="visualDebug" class="visual-debug" style="display: none;">
                <div id="contentVisual" class="content-visual">المحتوى الرئيسي</div>
                <div id="sidebarVisual" class="sidebar-visual">الشريط الجانبي</div>
            </div>
        </div>

        <!-- Quick Fix -->
        <div class="debug-section">
            <h3><i class="fas fa-wrench"></i> إصلاح سريع</h3>
            <button class="debug-button fix-button" onclick="applyQuickFix()">
                <i class="fas fa-magic"></i> تطبيق إصلاح فوري
            </button>
            <button class="debug-button" onclick="testFix()">
                <i class="fas fa-test-tube"></i> اختبار الإصلاح
            </button>
            <div id="quickFixResult"></div>
        </div>

        <!-- Advanced Diagnostics -->
        <div class="debug-section">
            <h3><i class="fas fa-microscope"></i> تشخيص متقدم</h3>
            <button class="debug-button" onclick="runAdvancedDiagnostics()">
                <i class="fas fa-play"></i> تشخيص شامل
            </button>
            <div id="advancedDiagnostics"></div>
        </div>
    </div>

    <script>
        let adminWindow = null;

        function analyzeCurrentLayout() {
            const resultDiv = document.getElementById('layoutAnalysis');
            const visualDiv = document.getElementById('visualDebug');
            
            resultDiv.innerHTML = '<div class="info">جاري فتح لوحة التحكم وتحليل التخطيط...</div>';
            
            // Open admin page
            adminWindow = window.open('index.html', 'adminWindow', 'width=1200,height=800');
            
            setTimeout(() => {
                try {
                    const mainContent = adminWindow.document.querySelector('.main-content');
                    const sidebar = adminWindow.document.querySelector('.sidebar');
                    
                    if (!mainContent || !sidebar) {
                        resultDiv.innerHTML = '<div class="error">❌ لم يتم العثور على عناصر التخطيط</div>';
                        return;
                    }

                    const mainStyles = adminWindow.getComputedStyle(mainContent);
                    const sidebarStyles = adminWindow.getComputedStyle(sidebar);
                    const windowWidth = adminWindow.innerWidth;
                    
                    const analysis = {
                        windowWidth: windowWidth,
                        sidebarWidth: parseInt(sidebarStyles.width),
                        mainMarginRight: parseInt(mainStyles.marginRight),
                        mainPaddingRight: parseInt(mainStyles.paddingRight),
                        mainWidth: parseInt(mainStyles.width),
                        sidebarPosition: sidebarStyles.position,
                        sidebarRight: sidebarStyles.right,
                        overlap: false
                    };
                    
                    // Check for overlap
                    const contentRightEdge = windowWidth - analysis.mainMarginRight;
                    const sidebarLeftEdge = windowWidth - analysis.sidebarWidth;
                    analysis.overlap = contentRightEdge > sidebarLeftEdge;
                    
                    let resultHTML = '<div class="layout-info">';
                    resultHTML += `عرض النافذة: ${analysis.windowWidth}px\n`;
                    resultHTML += `عرض الشريط الجانبي: ${analysis.sidebarWidth}px\n`;
                    resultHTML += `هامش المحتوى الأيمن: ${analysis.mainMarginRight}px\n`;
                    resultHTML += `حشو المحتوى الأيمن: ${analysis.mainPaddingRight}px\n`;
                    resultHTML += `عرض المحتوى: ${analysis.mainWidth}px\n`;
                    resultHTML += `موضع الشريط الجانبي: ${analysis.sidebarPosition}\n`;
                    resultHTML += `موضع الشريط الأيمن: ${analysis.sidebarRight}\n`;
                    resultHTML += '</div>';
                    
                    if (analysis.overlap) {
                        resultHTML += '<div class="error">❌ يوجد تداخل! المحتوى مخفي خلف الشريط الجانبي</div>';
                        resultHTML += '<div class="warning">المشكلة: هامش المحتوى (' + analysis.mainMarginRight + 'px) أقل من عرض الشريط الجانبي (' + analysis.sidebarWidth + 'px)</div>';
                    } else {
                        resultHTML += '<div class="success">✅ لا يوجد تداخل - التخطيط يبدو صحيحاً</div>';
                    }
                    
                    resultDiv.innerHTML = resultHTML;
                    
                    // Show visual representation
                    visualDiv.style.display = 'block';
                    const contentVisual = document.getElementById('contentVisual');
                    const sidebarVisual = document.getElementById('sidebarVisual');
                    
                    const sidebarPercentage = (analysis.sidebarWidth / windowWidth) * 100;
                    const contentMarginPercentage = (analysis.mainMarginRight / windowWidth) * 100;
                    
                    sidebarVisual.style.width = sidebarPercentage + '%';
                    contentVisual.style.marginRight = contentMarginPercentage + '%';
                    
                    if (analysis.overlap) {
                        contentVisual.style.background = '#fee2e2';
                        contentVisual.innerHTML = 'المحتوى المخفي';
                    }
                    
                } catch (error) {
                    resultDiv.innerHTML = '<div class="error">❌ خطأ في التحليل: ' + error.message + '</div>';
                }
            }, 2000);
        }

        function applyQuickFix() {
            const resultDiv = document.getElementById('quickFixResult');
            
            if (!adminWindow || adminWindow.closed) {
                resultDiv.innerHTML = '<div class="warning">يرجى تشغيل تحليل التخطيط أولاً</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">جاري تطبيق الإصلاح...</div>';
            
            try {
                // Apply emergency CSS fix
                const style = adminWindow.document.createElement('style');
                style.id = 'emergency-layout-fix';
                style.textContent = `
                    .main-content {
                        margin-right: 400px !important;
                        padding-right: 120px !important;
                        width: calc(100% - 400px) !important;
                        max-width: calc(100vw - 400px) !important;
                        box-sizing: border-box !important;
                        position: relative !important;
                    }
                    .sidebar {
                        width: 380px !important;
                        position: fixed !important;
                        right: 0 !important;
                        top: 0 !important;
                        height: 100vh !important;
                        z-index: 100 !important;
                        box-shadow: -10px 0 30px rgba(0,0,0,0.3) !important;
                    }
                    .content-section {
                        width: 100% !important;
                        margin-right: 0 !important;
                        box-sizing: border-box !important;
                    }
                `;
                
                // Remove existing fix if any
                const existingFix = adminWindow.document.getElementById('emergency-layout-fix');
                if (existingFix) {
                    existingFix.remove();
                }
                
                adminWindow.document.head.appendChild(style);
                
                resultDiv.innerHTML = '<div class="success">✅ تم تطبيق الإصلاح بنجاح!</div>';
                
                // Test the fix after a short delay
                setTimeout(() => {
                    testFix();
                }, 500);
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في تطبيق الإصلاح: ' + error.message + '</div>';
            }
        }

        function testFix() {
            if (!adminWindow || adminWindow.closed) {
                document.getElementById('quickFixResult').innerHTML += '<div class="warning">النافذة مغلقة - لا يمكن اختبار الإصلاح</div>';
                return;
            }
            
            try {
                const mainContent = adminWindow.document.querySelector('.main-content');
                const sidebar = adminWindow.document.querySelector('.sidebar');
                
                const mainStyles = adminWindow.getComputedStyle(mainContent);
                const sidebarStyles = adminWindow.getComputedStyle(sidebar);
                
                const marginRight = parseInt(mainStyles.marginRight);
                const sidebarWidth = parseInt(sidebarStyles.width);
                
                let testResult = '<div class="layout-info">نتيجة الاختبار:\n';
                testResult += `هامش المحتوى: ${marginRight}px\n`;
                testResult += `عرض الشريط الجانبي: ${sidebarWidth}px\n`;
                testResult += '</div>';
                
                if (marginRight >= sidebarWidth + 50) {
                    testResult += '<div class="success">✅ الإصلاح نجح! المحتوى الآن مرئي بالكامل</div>';
                } else {
                    testResult += '<div class="error">❌ الإصلاح لم ينجح بالكامل - قد تحتاج تعديلات إضافية</div>';
                }
                
                document.getElementById('quickFixResult').innerHTML += testResult;
                
            } catch (error) {
                document.getElementById('quickFixResult').innerHTML += '<div class="error">❌ خطأ في اختبار الإصلاح: ' + error.message + '</div>';
            }
        }

        function runAdvancedDiagnostics() {
            const resultDiv = document.getElementById('advancedDiagnostics');
            
            if (!adminWindow || adminWindow.closed) {
                adminWindow = window.open('index.html', 'adminWindow', 'width=1200,height=800');
                resultDiv.innerHTML = '<div class="info">فتح نافذة جديدة للتشخيص...</div>';
                
                setTimeout(() => runAdvancedDiagnostics(), 3000);
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">جاري التشخيص المتقدم...</div>';
            
            try {
                const diagnostics = {
                    viewport: {
                        width: adminWindow.innerWidth,
                        height: adminWindow.innerHeight
                    },
                    elements: {},
                    styles: {},
                    issues: []
                };
                
                // Check main elements
                const mainContent = adminWindow.document.querySelector('.main-content');
                const sidebar = adminWindow.document.querySelector('.sidebar');
                const adminContainer = adminWindow.document.querySelector('.admin-container');
                
                if (mainContent) {
                    const styles = adminWindow.getComputedStyle(mainContent);
                    diagnostics.elements.mainContent = {
                        found: true,
                        styles: {
                            marginRight: styles.marginRight,
                            paddingRight: styles.paddingRight,
                            width: styles.width,
                            maxWidth: styles.maxWidth,
                            position: styles.position,
                            boxSizing: styles.boxSizing
                        }
                    };
                } else {
                    diagnostics.issues.push('Main content element not found');
                }
                
                if (sidebar) {
                    const styles = adminWindow.getComputedStyle(sidebar);
                    diagnostics.elements.sidebar = {
                        found: true,
                        styles: {
                            width: styles.width,
                            position: styles.position,
                            right: styles.right,
                            top: styles.top,
                            zIndex: styles.zIndex
                        }
                    };
                } else {
                    diagnostics.issues.push('Sidebar element not found');
                }
                
                // Check for layout issues
                if (mainContent && sidebar) {
                    const marginRight = parseInt(adminWindow.getComputedStyle(mainContent).marginRight);
                    const sidebarWidth = parseInt(adminWindow.getComputedStyle(sidebar).width);
                    
                    if (marginRight < sidebarWidth) {
                        diagnostics.issues.push(`Content margin (${marginRight}px) is less than sidebar width (${sidebarWidth}px)`);
                    }
                    
                    if (adminWindow.getComputedStyle(sidebar).position !== 'fixed') {
                        diagnostics.issues.push('Sidebar should have position: fixed');
                    }
                    
                    if (adminWindow.getComputedStyle(sidebar).right !== '0px') {
                        diagnostics.issues.push('Sidebar should be positioned at right: 0');
                    }
                }
                
                // Generate report
                let report = '<div class="layout-info">';
                report += 'تقرير التشخيص المتقدم:\n\n';
                report += `عرض النافذة: ${diagnostics.viewport.width}px\n`;
                report += `ارتفاع النافذة: ${diagnostics.viewport.height}px\n\n`;
                
                if (diagnostics.elements.mainContent) {
                    report += 'المحتوى الرئيسي:\n';
                    Object.entries(diagnostics.elements.mainContent.styles).forEach(([key, value]) => {
                        report += `  ${key}: ${value}\n`;
                    });
                    report += '\n';
                }
                
                if (diagnostics.elements.sidebar) {
                    report += 'الشريط الجانبي:\n';
                    Object.entries(diagnostics.elements.sidebar.styles).forEach(([key, value]) => {
                        report += `  ${key}: ${value}\n`;
                    });
                    report += '\n';
                }
                
                report += '</div>';
                
                if (diagnostics.issues.length > 0) {
                    report += '<div class="error">المشاكل المكتشفة:\n';
                    diagnostics.issues.forEach(issue => {
                        report += `• ${issue}\n`;
                    });
                    report += '</div>';
                } else {
                    report += '<div class="success">✅ لم يتم اكتشاف مشاكل في التخطيط</div>';
                }
                
                resultDiv.innerHTML = report;
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في التشخيص المتقدم: ' + error.message + '</div>';
            }
        }

        // Auto-run analysis on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔧 Layout Debug Tool loaded');
            }, 1000);
        });
    </script>
</body>
</html>
