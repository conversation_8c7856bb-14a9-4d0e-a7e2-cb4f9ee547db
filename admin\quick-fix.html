<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح سريع - مشاكل Firebase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
        }
        
        .section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح سريع</h1>
            <p>حل مشاكل Firebase والتوجيه اللا نهائي</p>
        </div>

        <!-- Problem Detection -->
        <div class="section">
            <h3>🔍 تشخيص المشكلة</h3>
            <div id="problemStatus" class="status warning">جاري فحص المشاكل...</div>
            <button class="btn btn-primary" onclick="diagnoseProblem()">إعادة فحص</button>
        </div>

        <!-- Quick Solutions -->
        <div class="section">
            <h3>⚡ حلول سريعة</h3>
            
            <div class="status warning">
                <strong>المشكلة الأكثر شيوعاً:</strong> التوجيه اللا نهائي بين صفحات تسجيل الدخول ولوحة التحكم
            </div>
            
            <button class="btn btn-success" onclick="clearRedirectLoop()">مسح حلقة التوجيه</button>
            <button class="btn btn-warning" onclick="resetFirebase()">إعادة تعيين Firebase</button>
            <button class="btn btn-danger" onclick="clearAllData()">مسح جميع البيانات</button>
        </div>

        <!-- Direct Access -->
        <div class="section">
            <h3>🚀 وصول مباشر</h3>
            <div class="quick-links">
                <a href="firebase-test.html" class="btn btn-primary">اختبار Firebase</a>
                <a href="setup-firebase-admins.html" class="btn btn-success">إعداد المديرين</a>
                <a href="login.html?bypass=true" class="btn btn-warning">تسجيل دخول مباشر</a>
                <a href="../index.html" class="btn btn-primary">الصفحة الرئيسية</a>
            </div>
        </div>

        <!-- Manual Login -->
        <div class="section">
            <h3>🔐 تسجيل دخول يدوي</h3>
            <p>إذا كانت صفحات تسجيل الدخول لا تعمل، استخدم هذا النموذج:</p>
            
            <div style="margin: 15px 0;">
                <input type="email" id="manualEmail" placeholder="البريد الإلكتروني" style="width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px;">
                <input type="password" id="manualPassword" placeholder="كلمة المرور" style="width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px;">
                <button class="btn btn-success" onclick="manualLogin()" style="width: 100%;">تسجيل دخول يدوي</button>
            </div>
            
            <div class="status warning">
                <strong>حسابات تجريبية:</strong><br>
                <EMAIL> / Admin123!@#<br>
                <EMAIL> / Demo123!
            </div>
        </div>

        <!-- Status Display -->
        <div class="section">
            <h3>📊 حالة النظام</h3>
            <div id="systemStatus">جاري فحص النظام...</div>
        </div>
    </div>

    <script>
        // Clear redirect loop
        function clearRedirectLoop() {
            sessionStorage.clear();
            localStorage.clear();
            
            // Clear specific redirect counters
            sessionStorage.removeItem('auth_redirect_count');
            localStorage.removeItem('firebase_auth_state');
            
            document.getElementById('problemStatus').className = 'status success';
            document.getElementById('problemStatus').textContent = '✅ تم مسح حلقة التوجيه بنجاح';
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        }

        // Reset Firebase
        function resetFirebase() {
            // Clear Firebase-related storage
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.includes('firebase') || key.includes('auth')) {
                    localStorage.removeItem(key);
                }
            });
            
            const sessionKeys = Object.keys(sessionStorage);
            sessionKeys.forEach(key => {
                if (key.includes('firebase') || key.includes('auth')) {
                    sessionStorage.removeItem(key);
                }
            });
            
            document.getElementById('problemStatus').className = 'status success';
            document.getElementById('problemStatus').textContent = '✅ تم إعادة تعيين Firebase بنجاح';
            
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }

        // Clear all data
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ سيتم تسجيل خروجك من جميع الحسابات.')) {
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                document.getElementById('problemStatus').className = 'status success';
                document.getElementById('problemStatus').textContent = '✅ تم مسح جميع البيانات بنجاح';
                
                setTimeout(() => {
                    window.location.href = 'setup-firebase-admins.html';
                }, 2000);
            }
        }

        // Manual login
        function manualLogin() {
            const email = document.getElementById('manualEmail').value;
            const password = document.getElementById('manualPassword').value;
            
            if (!email || !password) {
                alert('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }
            
            // Store credentials temporarily
            sessionStorage.setItem('manual_login_email', email);
            sessionStorage.setItem('manual_login_password', password);
            
            // Redirect to login page with manual flag
            window.location.href = 'login.html?manual=true';
        }

        // Diagnose problem
        function diagnoseProblem() {
            const statusDiv = document.getElementById('problemStatus');
            const redirectCount = sessionStorage.getItem('auth_redirect_count') || '0';
            const currentUrl = window.location.href;
            
            let problems = [];
            
            if (parseInt(redirectCount) > 2) {
                problems.push('حلقة توجيه لا نهائية مكتشفة');
            }
            
            if (currentUrl.includes('admin') && !localStorage.getItem('firebase_auth_state')) {
                problems.push('حالة المصادقة مفقودة');
            }
            
            if (problems.length === 0) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ لم يتم اكتشاف مشاكل واضحة';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ مشاكل مكتشفة: ' + problems.join(', ');
            }
        }

        // Check system status
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let status = [];
            
            // Check localStorage
            const localStorageSize = JSON.stringify(localStorage).length;
            status.push(`حجم التخزين المحلي: ${localStorageSize} بايت`);
            
            // Check sessionStorage
            const sessionStorageSize = JSON.stringify(sessionStorage).length;
            status.push(`حجم تخزين الجلسة: ${sessionStorageSize} بايت`);
            
            // Check redirect count
            const redirectCount = sessionStorage.getItem('auth_redirect_count') || '0';
            status.push(`عدد التوجيهات: ${redirectCount}`);
            
            // Check current page
            status.push(`الصفحة الحالية: ${window.location.pathname}`);
            
            statusDiv.innerHTML = status.join('<br>');
        }

        // Initialize
        setTimeout(() => {
            diagnoseProblem();
            checkSystemStatus();
        }, 500);
    </script>
</body>
</html>
